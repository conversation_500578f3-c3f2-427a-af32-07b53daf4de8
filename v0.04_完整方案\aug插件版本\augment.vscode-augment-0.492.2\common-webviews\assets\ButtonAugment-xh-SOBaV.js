import{S as Y,i as D,s as E,a as x,I as V,L as A,g as F,a0 as H,u as d,t as m,M as W,N as G,O as J,j as K,a8 as g,G as h,Q as I,c as f,Y as M,e as p,f as N,q as b,r as z,h as v,P as C,R as j,V as w,W as y,T as U}from"./SpinnerAugment-BEPEN2tu.js";import{B as X}from"./BaseButton-DBHsDlhs.js";const Z=s=>({}),O=s=>({}),_=s=>({}),P=s=>({});function Q(s){let t,l;const c=s[10].iconLeft,o=C(c,s,s[20],P);return{c(){t=h("div"),o&&o.c(),f(t,"class","c-button--icon svelte-1u3rjsd")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&j(o,c,i,i[20],l?y(c,i[20],a,_):w(i[20]),P)},i(i){l||(d(o,i),l=!0)},o(i){m(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function S(s){let t,l,c;return l=new U({props:{size:s[0]===.5?1:s[0],weight:s[1]==="ghost"?"regular":"medium",$$slots:{default:[tt]},$$scope:{ctx:s}}}),{c(){t=h("div"),V(l.$$.fragment),f(t,"class","c-button--text svelte-1u3rjsd")},m(o,i){p(o,t,i),A(l,t,null),c=!0},p(o,i){const a={};1&i&&(a.size=o[0]===.5?1:o[0]),2&i&&(a.weight=o[1]==="ghost"?"regular":"medium"),1048576&i&&(a.$$scope={dirty:i,ctx:o}),l.$set(a)},i(o){c||(d(l.$$.fragment,o),c=!0)},o(o){m(l.$$.fragment,o),c=!1},d(o){o&&v(t),W(l)}}}function tt(s){let t;const l=s[10].default,c=C(l,s,s[20],null);return{c(){c&&c.c()},m(o,i){c&&c.m(o,i),t=!0},p(o,i){c&&c.p&&(!t||1048576&i)&&j(c,l,o,o[20],t?y(l,o[20],i,null):w(o[20]),null)},i(o){t||(d(c,o),t=!0)},o(o){m(c,o),t=!1},d(o){c&&c.d(o)}}}function T(s){let t,l;const c=s[10].iconRight,o=C(c,s,s[20],O);return{c(){t=h("div"),o&&o.c(),f(t,"class","c-button--icon svelte-1u3rjsd")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&j(o,c,i,i[20],l?y(c,i[20],a,Z):w(i[20]),O)},i(i){l||(d(o,i),l=!0)},o(i){m(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function it(s){let t,l,c,o,i,a=s[9].iconLeft&&Q(s),r=s[9].default&&S(s),u=s[9].iconRight&&T(s);return{c(){t=h("div"),a&&a.c(),l=I(),r&&r.c(),c=I(),u&&u.c(),f(t,"class",o=M(`c-button--content c-button--size-${s[0]}`)+" svelte-1u3rjsd")},m(n,$){p(n,t,$),a&&a.m(t,null),N(t,l),r&&r.m(t,null),N(t,c),u&&u.m(t,null),i=!0},p(n,$){n[9].iconLeft?a?(a.p(n,$),512&$&&d(a,1)):(a=Q(n),a.c(),d(a,1),a.m(t,l)):a&&(b(),m(a,1,1,()=>{a=null}),z()),n[9].default?r?(r.p(n,$),512&$&&d(r,1)):(r=S(n),r.c(),d(r,1),r.m(t,c)):r&&(b(),m(r,1,1,()=>{r=null}),z()),n[9].iconRight?u?(u.p(n,$),512&$&&d(u,1)):(u=T(n),u.c(),d(u,1),u.m(t,null)):u&&(b(),m(u,1,1,()=>{u=null}),z()),(!i||1&$&&o!==(o=M(`c-button--content c-button--size-${n[0]}`)+" svelte-1u3rjsd"))&&f(t,"class",o)},i(n){i||(d(a),d(r),d(u),i=!0)},o(n){m(a),m(r),m(u),i=!1},d(n){n&&v(t),a&&a.d(),r&&r.d(),u&&u.d()}}}function ot(s){let t,l;const c=[{size:s[0]},{variant:s[1]},{color:s[2]},{highContrast:s[3]},{disabled:s[4]},{loading:s[6]},{alignment:s[7]},{radius:s[5]},s[8]];let o={$$slots:{default:[it]},$$scope:{ctx:s}};for(let i=0;i<c.length;i+=1)o=x(o,c[i]);return t=new X({props:o}),t.$on("click",s[11]),t.$on("keyup",s[12]),t.$on("keydown",s[13]),t.$on("mousedown",s[14]),t.$on("mouseover",s[15]),t.$on("focus",s[16]),t.$on("mouseleave",s[17]),t.$on("blur",s[18]),t.$on("contextmenu",s[19]),{c(){V(t.$$.fragment)},m(i,a){A(t,i,a),l=!0},p(i,[a]){const r=511&a?F(c,[1&a&&{size:i[0]},2&a&&{variant:i[1]},4&a&&{color:i[2]},8&a&&{highContrast:i[3]},16&a&&{disabled:i[4]},64&a&&{loading:i[6]},128&a&&{alignment:i[7]},32&a&&{radius:i[5]},256&a&&H(i[8])]):{};1049091&a&&(r.$$scope={dirty:a,ctx:i}),t.$set(r)},i(i){l||(d(t.$$.fragment,i),l=!0)},o(i){m(t.$$.fragment,i),l=!1},d(i){W(t,i)}}}function st(s,t,l){const c=["size","variant","color","highContrast","disabled","radius","loading","alignment"];let o=G(t,c),{$$slots:i={},$$scope:a}=t;const r=J(i);let{size:u=2}=t,{variant:n="solid"}=t,{color:$="neutral"}=t,{highContrast:L=!1}=t,{disabled:R=!1}=t,{radius:k="medium"}=t,{loading:B=!1}=t,{alignment:q="center"}=t;return s.$$set=e=>{t=x(x({},t),K(e)),l(8,o=G(t,c)),"size"in e&&l(0,u=e.size),"variant"in e&&l(1,n=e.variant),"color"in e&&l(2,$=e.color),"highContrast"in e&&l(3,L=e.highContrast),"disabled"in e&&l(4,R=e.disabled),"radius"in e&&l(5,k=e.radius),"loading"in e&&l(6,B=e.loading),"alignment"in e&&l(7,q=e.alignment),"$$scope"in e&&l(20,a=e.$$scope)},[u,n,$,L,R,k,B,q,o,r,i,function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},function(e){g.call(this,s,e)},a]}class et extends Y{constructor(t){super(),D(this,t,st,ot,E,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5,loading:6,alignment:7})}}export{et as B};
