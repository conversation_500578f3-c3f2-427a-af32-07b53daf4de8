import{S as J,i as K,s as P,a9 as R,e as z,q as S,t as h,r as V,u as $,h as x,N as B,a as p,j as W,F as A,a8 as f,P as w,G as j,J as m,K as g,R as N,V as q,W as F,g as G,a5 as v,a7 as D}from"./SpinnerAugment-BEPEN2tu.js";import"./BaseButton-DBHsDlhs.js";function E(e){let n,a;const u=e[9].default,t=w(u,e,e[8],null);let r=[e[1]],c={};for(let i=0;i<r.length;i+=1)c=p(c,r[i]);return{c(){n=j("div"),t&&t.c(),m(n,c),g(n,"svelte-t4hy1o",!0)},m(i,o){z(i,n,o),t&&t.m(n,null),a=!0},p(i,o){t&&t.p&&(!a||256&o)&&N(t,u,i,i[8],a?F(u,i[8],o,null):q(i[8]),null),m(n,c=G(r,[2&o&&i[1]])),g(n,"svelte-t4hy1o",!0)},i(i){a||($(t,i),a=!0)},o(i){h(t,i),a=!1},d(i){i&&x(n),t&&t.d(i)}}}function H(e){let n,a,u,t;const r=e[9].default,c=w(r,e,e[8],null);let i=[e[1],{role:"button"},{tabindex:"0"}],o={};for(let l=0;l<i.length;l+=1)o=p(o,i[l]);return{c(){n=j("div"),c&&c.c(),m(n,o),g(n,"svelte-t4hy1o",!0)},m(l,d){z(l,n,d),c&&c.m(n,null),a=!0,u||(t=[v(n,"click",e[10]),v(n,"keyup",e[11]),v(n,"keydown",e[12]),v(n,"mousedown",e[13]),v(n,"mouseover",e[14]),v(n,"focus",e[15]),v(n,"mouseleave",e[16]),v(n,"blur",e[17]),v(n,"contextmenu",e[18])],u=!0)},p(l,d){c&&c.p&&(!a||256&d)&&N(c,r,l,l[8],a?F(r,l[8],d,null):q(l[8]),null),m(n,o=G(i,[2&d&&l[1],{role:"button"},{tabindex:"0"}])),g(n,"svelte-t4hy1o",!0)},i(l){a||($(c,l),a=!0)},o(l){h(c,l),a=!1},d(l){l&&x(n),c&&c.d(l),u=!1,D(t)}}}function I(e){let n,a,u,t;const r=[H,E],c=[];function i(o,l){return o[0]?0:1}return n=i(e),a=c[n]=r[n](e),{c(){a.c(),u=R()},m(o,l){c[n].m(o,l),z(o,u,l),t=!0},p(o,[l]){let d=n;n=i(o),n===d?c[n].p(o,l):(S(),h(c[d],1,1,()=>{c[d]=null}),V(),a=c[n],a?a.p(o,l):(a=c[n]=r[n](o),a.c()),$(a,1),a.m(u.parentNode,u))},i(o){t||($(a),t=!0)},o(o){h(a),t=!1},d(o){o&&x(u),c[n].d(o)}}}function L(e,n,a){let u,t,r;const c=["size","insetContent","variant","interactive","includeBackground"];let i=B(n,c),{$$slots:o={},$$scope:l}=n,{size:d=1}=n,{insetContent:k=!1}=n,{variant:y="surface"}=n,{interactive:C=!1}=n,{includeBackground:b=!0}=n;return e.$$set=s=>{n=p(p({},n),W(s)),a(19,i=B(n,c)),"size"in s&&a(2,d=s.size),"insetContent"in s&&a(3,k=s.insetContent),"variant"in s&&a(4,y=s.variant),"interactive"in s&&a(0,C=s.interactive),"includeBackground"in s&&a(5,b=s.includeBackground),"$$scope"in s&&a(8,l=s.$$scope)},e.$$.update=()=>{a(7,{class:u}=i,u),189&e.$$.dirty&&a(6,t=["c-card",`c-card--size-${d}`,`c-card--${y}`,k?"c-card--insetContent":"",C?"c-card--interactive":"",u,b?"c-card--with-background":""]),64&e.$$.dirty&&a(1,r={...A("accent"),class:t.join(" ")})},[C,r,d,k,y,b,t,u,l,o,function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)},function(s){f.call(this,e,s)}]}class Q extends J{constructor(n){super(),K(this,n,L,I,P,{size:2,insetContent:3,variant:4,interactive:0,includeBackground:5})}}export{Q as C};
