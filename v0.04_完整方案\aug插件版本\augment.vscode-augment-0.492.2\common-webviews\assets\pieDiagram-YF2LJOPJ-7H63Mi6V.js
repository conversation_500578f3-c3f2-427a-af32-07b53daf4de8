import{p as E}from"./chunk-TMUBEWPD-Dgr5PZ2V.js";import{X as y,O,aH as L,D as N,q as P,r as V,s as H,g as I,c as X,b as _,_ as m,l as R,x as G,d as J,E as K,I as Q,a5 as U,k as Y}from"./AugmentMessage-DtUuYhLr.js";import{p as Z}from"./gitGraph-YCYPL57B-VKBCM4bf.js";import{d as F}from"./arc-Dc36Pzro.js";import{o as tt}from"./ordinal-_rw2EY4v.js";import"./SpinnerAugment-BEPEN2tu.js";import"./CalloutAugment-DzngRWi1.js";import"./TextTooltipAugment-DAVq7Vla.js";import"./BaseButton-DBHsDlhs.js";import"./IconButtonAugment-DT9AU8SC.js";import"./Content-Bm7C6iJ1.js";import"./globals-D0QH3NT1.js";import"./arrow-up-right-from-square-Yo0BPgM2.js";import"./types-DK4HA_lx.js";import"./file-paths-BcSg4gks.js";import"./folder-DhY9vbAU.js";import"./github-kew-evZ8.js";import"./folder-opened-BLAu3pyz.js";import"./types-CGlLNakm.js";import"./file-type-utils-B3gunxPI.js";import"./check-56hMuF8e.js";import"./types-DDm27S8B.js";import"./index-CiMDylqQ.js";import"./utils-Rh_q5w_c.js";import"./ra-diff-ops-model-pDvb0wIq.js";import"./index-DY0Q9XhW.js";import"./CardAugment-RCmwRtRa.js";import"./isObjectLike-C4kUqRHQ.js";import"./TextAreaAugment-C1Wf9cvH.js";import"./diff-utils-CHDUdEQq.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-oyQWDnzB.js";import"./keypress-DD1aQVr0.js";import"./await_block-DO_bDmS_.js";import"./CollapseButtonAugment-CEo2LYfW.js";import"./ButtonAugment-xh-SOBaV.js";import"./MaterialIcon-BGa1zPPN.js";import"./CopyButton-BVbGAvmE.js";import"./magnifying-glass-BmBw0jRN.js";import"./ellipsis-DsAFryUF.js";import"./IconFilePath-BCQiczSW.js";import"./LanguageIcon-CV8JBzL9.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-CId0aUQb.js";import"./lodash-CujEqTm9.js";import"./mcp-logo-C2xFPiFq.js";import"./terminal-CS_V-nAg.js";import"./pen-to-square-DEUxz3in.js";import"./augment-logo-DTgokSKV.js";import"./_baseUniq-83Ljorp-.js";import"./_basePickBy-DTm0XUfP.js";import"./clone-CURaMU1-.js";import"./init-g68aIKmP.js";function et(t,r){return r<t?-1:r>t?1:r>=t?0:NaN}function rt(t){return t}var W=N.pie,B={sections:new Map,showData:!1,config:W},M=B.sections,z=B.showData,it=structuredClone(W),j={getConfig:m(()=>structuredClone(it),"getConfig"),clear:m(()=>{M=new Map,z=B.showData,G()},"clear"),setDiagramTitle:P,getDiagramTitle:V,setAccTitle:H,getAccTitle:I,setAccDescription:X,getAccDescription:_,addSection:m(({label:t,value:r})=>{M.has(t)||(M.set(t,r),R.debug(`added new section: ${t}, with value: ${r}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{z=t},"setShowData"),getShowData:m(()=>z,"getShowData")},at=m((t,r)=>{E(t,r),r.setShowData(t.showData),t.sections.map(r.addSection)},"populateDb"),nt={parse:m(async t=>{const r=await Z("pie",t);R.debug(r),at(r,j)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),pt=m(t=>{const r=[...t.entries()].map(p=>({label:p[0],value:p[1]})).sort((p,u)=>u.value-p.value);return function(){var p=rt,u=et,c=null,w=y(0),S=y(O),$=y(0);function i(e){var a,l,n,A,g,s=(e=L(e)).length,v=0,D=new Array(s),d=new Array(s),f=+w.apply(this,arguments),C=Math.min(O,Math.max(-O,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/s,$.apply(this,arguments)),b=h*(C<0?-1:1);for(a=0;a<s;++a)(g=d[D[a]=a]=+p(e[a],a,e))>0&&(v+=g);for(u!=null?D.sort(function(x,T){return u(d[x],d[T])}):c!=null&&D.sort(function(x,T){return c(e[x],e[T])}),a=0,n=v?(C-s*b)/v:0;a<s;++a,f=A)l=D[a],A=f+((g=d[l])>0?g*n:0)+b,d[l]={data:e[l],index:a,value:g,startAngle:f,endAngle:A,padAngle:h};return d}return i.value=function(e){return arguments.length?(p=typeof e=="function"?e:y(+e),i):p},i.sortValues=function(e){return arguments.length?(u=e,c=null,i):u},i.sort=function(e){return arguments.length?(c=e,u=null,i):c},i.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),i):w},i.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),i):S},i.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),i):$},i}().value(p=>p.value)(r)},"createPieArcs"),le={parser:nt,db:j,renderer:{draw:m((t,r,p,u)=>{R.debug(`rendering pie chart
`+t);const c=u.db,w=J(),S=K(c.getConfig(),w.pie),$=18,i=450,e=i,a=Q(r),l=a.append("g");l.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[A]=U(n.pieOuterStrokeWidth);A??(A=2);const g=S.textPosition,s=Math.min(e,i)/2-40,v=F().innerRadius(0).outerRadius(s),D=F().innerRadius(s*g).outerRadius(s*g);l.append("circle").attr("cx",0).attr("cy",0).attr("r",s+A/2).attr("class","pieOuterCircle");const d=c.getSections(),f=pt(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=tt(C);l.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),l.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+D.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),l.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=l.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:q}=o.data;return c.getShowData()?`${k} [${q}]`:k});const T=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));a.attr("viewBox",`0 0 ${T} 450`),Y(a,i,T,S.useMaxWidth)},"draw")},styles:ot};export{le as diagram};
