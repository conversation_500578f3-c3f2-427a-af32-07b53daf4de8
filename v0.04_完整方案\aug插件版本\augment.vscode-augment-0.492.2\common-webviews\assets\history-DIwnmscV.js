var ke=Object.defineProperty;var Ne=(e,t,n)=>t in e?ke(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var _t=(e,t,n)=>Ne(e,typeof t!="symbol"?t+"":t,n);import{S as it,i as st,s as ct,G as T,I as Y,c as N,K as V,e as I,L as E,a5 as G,u as M,t as D,h as W,M as j,X as H,Q as _,f as $,Z as X,n as J,ae as qt,a7 as $t,a2 as Me,_ as me,$ as ge,q as ot,r as at,a1 as he,aF as wt,ab as pe,a9 as $e}from"./SpinnerAugment-BEPEN2tu.js";import"./design-system-init-BG5cEFim.js";import"./design-system-init-DWM9BlRb.js";import{e as tt,h as nt,W as Z,u as Pe,o as qe}from"./BaseButton-DBHsDlhs.js";import{M as De}from"./MaterialIcon-BGa1zPPN.js";import{o as pt}from"./keypress-DD1aQVr0.js";import{c as Te,S as Dt,M as Re}from"./index-oyQWDnzB.js";import{e as Se,R as yt}from"./toggleHighContrast-Th-X2FgN.js";import{T as Ce}from"./TextAreaAugment-C1Wf9cvH.js";import{D as gt}from"./index-DY0Q9XhW.js";import{B as Ae}from"./ButtonAugment-xh-SOBaV.js";import{C as Yt}from"./next-edit-types-904A5ehg.js";import"./index-CiMDylqQ.js";import"./preload-helper-Dv6uf1Os.js";import"./Content-Bm7C6iJ1.js";import"./globals-D0QH3NT1.js";import"./CardAugment-RCmwRtRa.js";function K(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}function ft(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}const be=6048e5,Ie=864e5,Et=6e4,jt=36e5;let We={};function Rt(){return We}function Mt(e,t){var i,s,u,f;const n=Rt(),r=(t==null?void 0:t.weekStartsOn)??((s=(i=t==null?void 0:t.locale)==null?void 0:i.options)==null?void 0:s.weekStartsOn)??n.weekStartsOn??((f=(u=n.locale)==null?void 0:u.options)==null?void 0:f.weekStartsOn)??0,o=K(e),a=o.getDay(),c=(a<r?7:0)+a-r;return o.setDate(o.getDate()-c),o.setHours(0,0,0,0),o}function Tt(e){return Mt(e,{weekStartsOn:1})}function we(e){const t=K(e),n=t.getFullYear(),r=ft(e,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const o=Tt(r),a=ft(e,0);a.setFullYear(n,0,4),a.setHours(0,0,0,0);const c=Tt(a);return t.getTime()>=o.getTime()?n+1:t.getTime()>=c.getTime()?n:n-1}function Ot(e){const t=K(e);return t.setHours(0,0,0,0),t}function Ht(e){const t=K(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function Fe(e){if(t=e,!(t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"||typeof e=="number"))return!1;var t;const n=K(e);return!isNaN(Number(n))}const _e={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function St(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const Ye={date:St({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:St({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:St({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Ee={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function vt(e){return(t,n)=>{let r;if((n!=null&&n.context?String(n.context):"standalone")==="formatting"&&e.formattingValues){const o=e.defaultFormattingWidth||e.defaultWidth,a=n!=null&&n.width?String(n.width):o;r=e.formattingValues[a]||e.formattingValues[o]}else{const o=e.defaultWidth,a=n!=null&&n.width?String(n.width):e.defaultWidth;r=e.values[a]||e.values[o]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function xt(e){return(t,n={})=>{const r=n.width,o=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],a=t.match(o);if(!a)return null;const c=a[0],i=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],s=Array.isArray(i)?function(f,l){for(let d=0;d<f.length;d++)if(l(f[d]))return d}(i,f=>f.test(c)):function(f,l){for(const d in f)if(Object.prototype.hasOwnProperty.call(f,d)&&l(f[d]))return d}(i,f=>f.test(c));let u;return u=e.valueCallback?e.valueCallback(s):s,u=n.valueCallback?n.valueCallback(u):u,{value:u,rest:t.slice(c.length)}}}var kt;const je={code:"en-US",formatDistance:(e,t,n)=>{let r;const o=_e[e];return r=typeof o=="string"?o:t===1?o.one:o.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:Ye,formatRelative:(e,t,n,r)=>Ee[e],localize:{ordinalNumber:(e,t)=>{const n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:vt({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:vt({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:vt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:vt({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:vt({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(kt={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)},(e,t={})=>{const n=e.match(kt.matchPattern);if(!n)return null;const r=n[0],o=e.match(kt.parsePattern);if(!o)return null;let a=kt.valueCallback?kt.valueCallback(o[0]):o[0];return a=t.valueCallback?t.valueCallback(a):a,{value:a,rest:e.slice(r.length)}}),era:xt({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:xt({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:xt({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:xt({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:xt({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function Oe(e){const t=K(e);return function(r,o){const a=Ot(r),c=Ot(o),i=+a-Ht(a),s=+c-Ht(c);return Math.round((i-s)/Ie)}(t,function(r){const o=K(r),a=ft(r,0);return a.setFullYear(o.getFullYear(),0,1),a.setHours(0,0,0,0),a}(t))+1}function He(e){const t=K(e),n=+Tt(t)-+function(r){const o=we(r),a=ft(r,0);return a.setFullYear(o,0,4),a.setHours(0,0,0,0),Tt(a)}(t);return Math.round(n/be)+1}function ye(e,t){var f,l,d,h;const n=K(e),r=n.getFullYear(),o=Rt(),a=(t==null?void 0:t.firstWeekContainsDate)??((l=(f=t==null?void 0:t.locale)==null?void 0:f.options)==null?void 0:l.firstWeekContainsDate)??o.firstWeekContainsDate??((h=(d=o.locale)==null?void 0:d.options)==null?void 0:h.firstWeekContainsDate)??1,c=ft(e,0);c.setFullYear(r+1,0,a),c.setHours(0,0,0,0);const i=Mt(c,t),s=ft(e,0);s.setFullYear(r,0,a),s.setHours(0,0,0,0);const u=Mt(s,t);return n.getTime()>=i.getTime()?r+1:n.getTime()>=u.getTime()?r:r-1}function ze(e,t){const n=K(e),r=+Mt(n,t)-+function(o,a){var f,l,d,h;const c=Rt(),i=(a==null?void 0:a.firstWeekContainsDate)??((l=(f=a==null?void 0:a.locale)==null?void 0:f.options)==null?void 0:l.firstWeekContainsDate)??c.firstWeekContainsDate??((h=(d=c.locale)==null?void 0:d.options)==null?void 0:h.firstWeekContainsDate)??1,s=ye(o,a),u=ft(o,0);return u.setFullYear(s,0,i),u.setHours(0,0,0,0),Mt(u,a)}(n,t);return Math.round(r/be)+1}function O(e,t){return(e<0?"-":"")+Math.abs(e).toString().padStart(t,"0")}const rt={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return O(t==="yy"?r%100:r,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):O(n+1,2)},d:(e,t)=>O(e.getDate(),t.length),a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return n==="am"?"a.m.":"p.m."}},h:(e,t)=>O(e.getHours()%12||12,t.length),H:(e,t)=>O(e.getHours(),t.length),m:(e,t)=>O(e.getMinutes(),t.length),s:(e,t)=>O(e.getSeconds(),t.length),S(e,t){const n=t.length,r=e.getMilliseconds();return O(Math.trunc(r*Math.pow(10,n-3)),t.length)}},Le="midnight",Be="noon",Qe="morning",Ue="afternoon",Ge="evening",Xe="night",zt={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const r=e.getFullYear(),o=r>0?r:1-r;return n.ordinalNumber(o,{unit:"year"})}return rt.y(e,t)},Y:function(e,t,n,r){const o=ye(e,r),a=o>0?o:1-o;return t==="YY"?O(a%100,2):t==="Yo"?n.ordinalNumber(a,{unit:"year"}):O(a,t.length)},R:function(e,t){return O(we(e),t.length)},u:function(e,t){return O(e.getFullYear(),t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return O(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return O(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return rt.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return O(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const o=ze(e,r);return t==="wo"?n.ordinalNumber(o,{unit:"week"}):O(o,t.length)},I:function(e,t,n){const r=He(e);return t==="Io"?n.ordinalNumber(r,{unit:"week"}):O(r,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):rt.d(e,t)},D:function(e,t,n){const r=Oe(e);return t==="Do"?n.ordinalNumber(r,{unit:"dayOfYear"}):O(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const o=e.getDay(),a=(o-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(a);case"ee":return O(a,2);case"eo":return n.ordinalNumber(a,{unit:"day"});case"eee":return n.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(o,{width:"short",context:"formatting"});default:return n.day(o,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const o=e.getDay(),a=(o-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(a);case"cc":return O(a,t.length);case"co":return n.ordinalNumber(a,{unit:"day"});case"ccc":return n.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(o,{width:"narrow",context:"standalone"});case"cccccc":return n.day(o,{width:"short",context:"standalone"});default:return n.day(o,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),o=r===0?7:r;switch(t){case"i":return String(o);case"ii":return O(o,t.length);case"io":return n.ordinalNumber(o,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let o;switch(o=r===12?Be:r===0?Le:r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(o,{width:"narrow",context:"formatting"});default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let o;switch(o=r>=17?Ge:r>=12?Ue:r>=4?Qe:Xe,t){case"B":case"BB":case"BBB":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(o,{width:"narrow",context:"formatting"});default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let r=e.getHours()%12;return r===0&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return rt.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):rt.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return t==="Ko"?n.ordinalNumber(r,{unit:"hour"}):O(r,t.length)},k:function(e,t,n){let r=e.getHours();return r===0&&(r=24),t==="ko"?n.ordinalNumber(r,{unit:"hour"}):O(r,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):rt.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):rt.s(e,t)},S:function(e,t){return rt.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return Bt(r);case"XXXX":case"XX":return lt(r);default:return lt(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return Bt(r);case"xxxx":case"xx":return lt(r);default:return lt(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Lt(r,":");default:return"GMT"+lt(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Lt(r,":");default:return"GMT"+lt(r,":")}},t:function(e,t,n){return O(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,n){return O(e.getTime(),t.length)}};function Lt(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),o=Math.trunc(r/60),a=r%60;return a===0?n+String(o):n+String(o)+t+O(a,2)}function Bt(e,t){return e%60==0?(e>0?"-":"+")+O(Math.abs(e)/60,2):lt(e,t)}function lt(e,t=""){const n=e>0?"-":"+",r=Math.abs(e);return n+O(Math.trunc(r/60),2)+t+O(r%60,2)}const Qt=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},Ut=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},Je={p:Ut,P:(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],o=n[2];if(!o)return Qt(e,t);let a;switch(r){case"P":a=t.dateTime({width:"short"});break;case"PP":a=t.dateTime({width:"medium"});break;case"PPP":a=t.dateTime({width:"long"});break;default:a=t.dateTime({width:"full"})}return a.replace("{{date}}",Qt(r,t)).replace("{{time}}",Ut(o,t))}},Ze=/^D+$/,Ke=/^Y+$/,Ve=["D","DD","YY","YYYY"],tn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,en=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,nn=/^'([^]*?)'?$/,rn=/''/g,on=/[a-zA-Z]/;function Gt(e,t,n){var f,l,d,h;const r=Rt(),o=r.locale??je,a=r.firstWeekContainsDate??((l=(f=r.locale)==null?void 0:f.options)==null?void 0:l.firstWeekContainsDate)??1,c=r.weekStartsOn??((h=(d=r.locale)==null?void 0:d.options)==null?void 0:h.weekStartsOn)??0,i=K(e);if(!Fe(i))throw new RangeError("Invalid time value");let s=t.match(en).map(m=>{const p=m[0];return p==="p"||p==="P"?(0,Je[p])(m,o.formatLong):m}).join("").match(tn).map(m=>{if(m==="''")return{isToken:!1,value:"'"};const p=m[0];if(p==="'")return{isToken:!1,value:an(m)};if(zt[p])return{isToken:!0,value:m};if(p.match(on))throw new RangeError("Format string contains an unescaped latin alphabet character `"+p+"`");return{isToken:!1,value:m}});o.localize.preprocessor&&(s=o.localize.preprocessor(i,s));const u={firstWeekContainsDate:a,weekStartsOn:c,locale:o};return s.map(m=>{if(!m.isToken)return m.value;const p=m.value;return(function(w){return Ke.test(w)}(p)||function(w){return Ze.test(w)}(p))&&function(w,P,q){const v=function(k,A,x){const U=k[0]==="Y"?"years":"days of the month";return`Use \`${k.toLowerCase()}\` instead of \`${k}\` (in \`${A}\`) for formatting ${U} to the input \`${x}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(w,P,q);if(console.warn(v),Ve.includes(w))throw new RangeError(v)}(p,t,String(e)),(0,zt[p[0]])(i,p,o.localize,u)}).join("")}function an(e){const t=e.match(nn);return t?t[1].replace(rn,"'"):e}function Ct(e,t){const n=function(i){const s={},u=i.split(Pt.dateTimeDelimiter);let f;if(u.length>2)return s;if(/:/.test(u[0])?f=u[0]:(s.date=u[0],f=u[1],Pt.timeZoneDelimiter.test(s.date)&&(s.date=i.split(Pt.timeZoneDelimiter)[0],f=i.substr(s.date.length,i.length))),f){const l=Pt.timezone.exec(f);l?(s.time=f.replace(l[1],""),s.timezone=l[1]):s.time=f}return s}(e);let r;if(n.date){const i=function(s,u){const f=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+u)+"})|(\\d{2}|[+-]\\d{"+(2+u)+"})$)"),l=s.match(f);if(!l)return{year:NaN,restDateString:""};const d=l[1]?parseInt(l[1]):null,h=l[2]?parseInt(l[2]):null;return{year:h===null?d:100*h,restDateString:s.slice((l[1]||l[2]).length)}}(n.date,2);r=function(s,u){if(u===null)return new Date(NaN);const f=s.match(sn);if(!f)return new Date(NaN);const l=!!f[4],d=Nt(f[1]),h=Nt(f[2])-1,m=Nt(f[3]),p=Nt(f[4]),w=Nt(f[5])-1;if(l)return function(P,q,v){return q>=1&&q<=53&&v>=0&&v<=6}(0,p,w)?function(P,q,v){const k=new Date(0);k.setUTCFullYear(P,0,4);const A=k.getUTCDay()||7,x=7*(q-1)+v+1-A;return k.setUTCDate(k.getUTCDate()+x),k}(u,p,w):new Date(NaN);{const P=new Date(0);return function(q,v,k){return v>=0&&v<=11&&k>=1&&k<=(ln[v]||(Xt(q)?29:28))}(u,h,m)&&function(q,v){return v>=1&&v<=(Xt(q)?366:365)}(u,d)?(P.setUTCFullYear(u,h,Math.max(d,m)),P):new Date(NaN)}}(i.restDateString,i.year)}if(!r||isNaN(r.getTime()))return new Date(NaN);const o=r.getTime();let a,c=0;if(n.time&&(c=function(i){const s=i.match(cn);if(!s)return NaN;const u=At(s[1]),f=At(s[2]),l=At(s[3]);return function(d,h,m){return d===24?h===0&&m===0:m>=0&&m<60&&h>=0&&h<60&&d>=0&&d<25}(u,f,l)?u*jt+f*Et+1e3*l:NaN}(n.time),isNaN(c)))return new Date(NaN);if(!n.timezone){const i=new Date(o+c),s=new Date(0);return s.setFullYear(i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()),s.setHours(i.getUTCHours(),i.getUTCMinutes(),i.getUTCSeconds(),i.getUTCMilliseconds()),s}return a=function(i){if(i==="Z")return 0;const s=i.match(un);if(!s)return 0;const u=s[1]==="+"?-1:1,f=parseInt(s[2]),l=s[3]&&parseInt(s[3])||0;return function(d,h){return h>=0&&h<=59}(0,l)?u*(f*jt+l*Et):NaN}(n.timezone),isNaN(a)?new Date(NaN):new Date(o+c+a)}const Pt={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},sn=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,cn=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,un=/^([+-])(\d{2})(?::?(\d{2}))?$/;function Nt(e){return e?parseInt(e):1}function At(e){return e&&parseFloat(e.replace(",","."))||0}const ln=[31,null,31,30,31,30,31,31,30,31,30,31];function Xt(e){return e%400==0||e%4==0&&e%100!=0}var Q=(e=>(e[e.unset=0]="unset",e[e.positive=1]="positive",e[e.negative=2]="negative",e))(Q||{});function dn(e){let t,n,r,o,a,c;return n=new De({props:{iconName:e[6],fill:e[0]===e[1],class:"c-rating-btn__icon"}}),{c(){t=T("button"),Y(n.$$.fragment),N(t,"class",r="c-rating-btn "+e[7]+" svelte-x9npwv"),N(t,"title",e[5]),t.disabled=e[2],V(t,"c-rating-btn--selected",e[0]===e[1]),V(t,"c-rating-btn--hide",e[3])},m(i,s){I(i,t,s),E(n,t,null),o=!0,a||(c=G(t,"click",e[8]),a=!0)},p(i,[s]){const u={};64&s&&(u.iconName=i[6]),3&s&&(u.fill=i[0]===i[1]),n.$set(u),(!o||128&s&&r!==(r="c-rating-btn "+i[7]+" svelte-x9npwv"))&&N(t,"class",r),(!o||32&s)&&N(t,"title",i[5]),(!o||4&s)&&(t.disabled=i[2]),(!o||131&s)&&V(t,"c-rating-btn--selected",i[0]===i[1]),(!o||136&s)&&V(t,"c-rating-btn--hide",i[3])},i(i){o||(M(n.$$.fragment,i),o=!0)},o(i){D(n.$$.fragment,i),o=!1},d(i){i&&W(t),j(n),a=!1,c()}}}function fn(e,t,n){let r,o,a,{selected:c}=t,{rating:i}=t,{disabled:s=!1}=t,{hide:u=!1}=t,{click:f}=t;switch(i){case Q.negative:r="Report completion as bad",o="thumb_down",a="c-rating-btn--negative";break;case Q.positive:r="Report completion as good",o="thumb_up",a="c-rating-btn--positive";break;case Q.unset:r="Send feedback about this completion",o="send",a="c-rating-btn--send"}return e.$$set=l=>{"selected"in l&&n(0,c=l.selected),"rating"in l&&n(1,i=l.rating),"disabled"in l&&n(2,s=l.disabled),"hide"in l&&n(3,u=l.hide),"click"in l&&n(4,f=l.click)},[c,i,s,u,f,r,o,a,()=>f(i)]}class ht extends it{constructor(t){super(),st(this,t,fn,dn,ct,{selected:0,rating:1,disabled:2,hide:3,click:4})}}const Jt=["Thanks for the feedback!","Thanks for improving Augment!","Thanks for taking the time!","Thanks for helping Augment improve!","Thanks for helping us enhance Augment!","We value your input. Thanks for improving Augment!","Your insights are making a difference. Cheers!"];function ve(){return Jt[Math.floor(Math.random()*Jt.length)]}function Zt(e,t,n){const r=e.slice();return r[8]=t[n],r}function Kt(e){let t,n,r,o,a,c;return{c(){t=T("li"),n=T("div"),r=H("‎"),o=H(e[2]),N(n,"class","c-history-header__link c-history-header--ellipsis-left svelte-1e2bfen"),N(n,"role","button"),N(n,"tabindex","0"),N(t,"class","svelte-1e2bfen")},m(i,s){I(i,t,s),$(t,n),$(n,r),$(n,o),a||(c=[G(n,"click",e[6]),G(n,"keydown",pt("Enter",e[6]))],a=!0)},p(i,s){4&s&&X(o,i[2])},d(i){i&&W(t),a=!1,$t(c)}}}function Vt(e){let t,n,r,o;return{c(){t=T("li"),n=T("div"),r=H("Instruction: "),o=H(e[3]),N(n,"class","c-history-header--ellipsis svelte-1e2bfen"),N(t,"class","svelte-1e2bfen")},m(a,c){I(a,t,c),$(t,n),$(n,r),$(n,o)},p(a,c){8&c&&X(o,a[3])},d(a){a&&W(t)}}}function te(e){let t,n,r,o,a=e[8]+"";return{c(){t=T("li"),n=T("div"),r=H(a),o=_(),N(n,"class","c-history-header--ellipsis svelte-1e2bfen"),N(t,"class","svelte-1e2bfen")},m(c,i){I(c,t,i),$(t,n),$(n,r),$(t,o)},p(c,i){16&i&&a!==(a=c[8]+"")&&X(r,a)},d(c){c&&W(t)}}}function mn(e){let t,n,r,o,a,c,i,s,u,f,l,d,h,m,p=Gt(e[0],"p 'on' P")+"",w=e[2]&&Kt(e),P=e[3]&&Vt(e),q=tt(e[4]),v=[];for(let k=0;k<q.length;k+=1)v[k]=te(Zt(e,q,k));return{c(){t=T("div"),n=T("div"),r=H(p),o=_(),a=T("ul"),c=T("li"),i=T("div"),s=H("Request ID: "),u=H(e[1]),f=_(),w&&w.c(),l=_(),P&&P.c(),d=_();for(let k=0;k<v.length;k+=1)v[k].c();N(i,"class","c-history-header__link c-history-header--ellipsis svelte-1e2bfen"),N(i,"role","button"),N(i,"tabindex","0"),N(c,"class","svelte-1e2bfen"),N(a,"class","svelte-1e2bfen"),N(t,"class","c-history-header svelte-1e2bfen")},m(k,A){I(k,t,A),$(t,n),$(n,r),$(t,o),$(t,a),$(a,c),$(c,i),$(i,s),$(i,u),$(a,f),w&&w.m(a,null),$(a,l),P&&P.m(a,null),$(a,d);for(let x=0;x<v.length;x+=1)v[x]&&v[x].m(a,null);h||(m=[G(i,"click",e[5]),G(i,"keydown",pt("Enter",e[5]))],h=!0)},p(k,[A]){if(1&A&&p!==(p=Gt(k[0],"p 'on' P")+"")&&X(r,p),2&A&&X(u,k[1]),k[2]?w?w.p(k,A):(w=Kt(k),w.c(),w.m(a,l)):w&&(w.d(1),w=null),k[3]?P?P.p(k,A):(P=Vt(k),P.c(),P.m(a,d)):P&&(P.d(1),P=null),16&A){let x;for(q=tt(k[4]),x=0;x<q.length;x+=1){const U=Zt(k,q,x);v[x]?v[x].p(U,A):(v[x]=te(U),v[x].c(),v[x].m(a,null))}for(;x<v.length;x+=1)v[x].d(1);v.length=q.length}},i:J,o:J,d(k){k&&W(t),w&&w.d(),P&&P.d(),qt(v,k),h=!1,$t(m)}}}function gn(e,t,n){let{occuredAt:r}=t,{requestID:o}=t,{pathName:a=""}=t,{repoRoot:c}=t,{prompt:i=""}=t,{others:s=[]}=t;return e.$$set=u=>{"occuredAt"in u&&n(0,r=u.occuredAt),"requestID"in u&&n(1,o=u.requestID),"pathName"in u&&n(2,a=u.pathName),"repoRoot"in u&&n(7,c=u.repoRoot),"prompt"in u&&n(3,i=u.prompt),"others"in u&&n(4,s=u.others)},[r,o,a,i,s,function(){nt.postMessage({type:Z.copyRequestID,data:o})},function(){nt.postMessage({type:Z.openFile,data:{repoRoot:c,pathName:a}})},c]}class It extends it{constructor(t){super(),st(this,t,gn,mn,ct,{occuredAt:0,requestID:1,pathName:2,repoRoot:7,prompt:3,others:4})}}const Wt={lineNumbers:"off",padding:{top:18,bottom:18}};function hn(e){let t,n,r;return n=new Te({props:{options:Wt,model:e[0],decorations:e[1]}}),{c(){t=T("div"),Y(n.$$.fragment),N(t,"class","c-completion-code-block")},m(o,a){I(o,t,a),E(n,t,null),r=!0},p:J,i(o){r||(M(n.$$.fragment,o),r=!0)},o(o){D(n.$$.fragment,o),r=!1},d(o){o&&W(t),j(n)}}}const ee=6;function pn(e,t,n){let{prefix:r}=t,{suffix:o}=t,{completion:a}=t;const c=function(g){const R=g.split(`
`).slice(-ee);for(let b=0;b<R.length;b++)if(R[b].trim().length>0)return R.slice(b).join(`
`);return""}(r),i=(s=o,!!(u=a.skippedSuffix)&&s.indexOf(u)===0);var s,u;const f=i?function(g,R){return R?g.indexOf(R)!==0?g:g.slice(R.length):g}(o,a.skippedSuffix):o,l=function(g){const R=g.split(`
`).slice(0,ee);for(let b=R.length-1;b>=0;b--)if(R[b].trim().length>0)return R.slice(0,b+1).join(`
`);return""}(f),d=a.text,h=i?a.skippedSuffix:"",m=a.suffixReplacementText,p=c+d+h+m+l,w=Se.createModel(p,"plaintext"),P=w.getPositionAt(0),q=w.getPositionAt(c.length),v=w.getPositionAt(c.length),k=w.getPositionAt(c.length+d.length),A=w.getPositionAt(c.length+d.length),x=w.getPositionAt(c.length+d.length+h.length),U=w.getPositionAt(c.length+d.length+h.length),S=w.getPositionAt(c.length+d.length+h.length+m.length),F=w.getPositionAt(c.length+d.length+h.length+m.length),et=w.getPositionAt(p.length),y=[{range:new yt(P.lineNumber,P.column,q.lineNumber,q.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new yt(F.lineNumber,F.column,et.lineNumber,et.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new yt(v.lineNumber,v.column,k.lineNumber,k.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new yt(U.lineNumber,U.column,S.lineNumber,S.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new yt(A.lineNumber,A.column,x.lineNumber,x.column),options:{inlineClassName:"c-completion-code-block--strikethrough"}}];return Me(()=>{w==null||w.dispose()}),e.$$set=g=>{"prefix"in g&&n(2,r=g.prefix),"suffix"in g&&n(3,o=g.suffix),"completion"in g&&n(4,a=g.completion)},[w,y,r,o,a]}class $n extends it{constructor(t){super(),st(this,t,pn,hn,ct,{prefix:2,suffix:3,completion:4})}}const dt=new class{constructor(){_t(this,"_state");this._state=nt.getState()||{},this._state.feedback=this._state.feedback||{}}getFeedback(e){return this._state.feedback[e]?this._state.feedback[e]:{selectedRating:Q.unset,feedbackNote:""}}setFeedback(e,t){this._state.feedback[e]=t,nt.setState(this._state)}cleanupFeedback(e){for(const t of Object.keys(this._state.feedback))e[t]||delete this._state.feedback[t];nt.setState(this._state)}};function ne(e,t,n){const r=e.slice();return r[22]=t[n],r}function re(e){let t,n;return t=new $n({props:{completion:e[22],prefix:e[0].prefix,suffix:e[0].suffix}}),{c(){Y(t.$$.fragment)},m(r,o){E(t,r,o),n=!0},p(r,o){const a={};1&o&&(a.completion=r[22]),1&o&&(a.prefix=r[0].prefix),1&o&&(a.suffix=r[0].suffix),t.$set(a)},i(r){n||(M(t.$$.fragment,r),n=!0)},o(r){D(t.$$.fragment,r),n=!1},d(r){j(t,r)}}}function oe(e){let t,n,r;function o(c){e[13](c)}let a={size:1,$$slots:{default:[Nn]},$$scope:{ctx:e}};return e[5]!==void 0&&(a.requestClose=e[5]),t=new gt.Root({props:a}),me.push(()=>ge(t,"requestClose",o)),{c(){Y(t.$$.fragment)},m(c,i){E(t,c,i),r=!0},p(c,i){const s={};33554432&i&&(s.$$scope={dirty:i,ctx:c}),!n&&32&i&&(n=!0,s.requestClose=c[5],he(()=>n=!1)),t.$set(s)},i(c){r||(M(t.$$.fragment,c),r=!0)},o(c){D(t.$$.fragment,c),r=!1},d(c){j(t,c)}}}function bn(e){let t;return{c(){t=H("Primary Issue")},m(n,r){I(n,t,r)},d(n){n&&W(t)}}}function wn(e){let t,n;return t=new Ae({props:{size:1,$$slots:{default:[bn]},$$scope:{ctx:e}}}),{c(){Y(t.$$.fragment)},m(r,o){E(t,r,o),n=!0},p(r,o){const a={};33554432&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a)},i(r){n||(M(t.$$.fragment,r),n=!0)},o(r){D(t.$$.fragment,r),n=!1},d(r){j(t,r)}}}function yn(e){let t;return{c(){t=H("(Optional) Primary issue")},m(n,r){I(n,t,r)},d(n){n&&W(t)}}}function vn(e){let t;return{c(){t=H("Bad formatting")},m(n,r){I(n,t,r)},d(n){n&&W(t)}}}function xn(e){let t;return{c(){t=H("Hallucination")},m(n,r){I(n,t,r)},d(n){n&&W(t)}}}function kn(e){let t,n,r,o,a,c;return t=new gt.Item({props:{onSelect:e[10],$$slots:{default:[yn]},$$scope:{ctx:e}}}),r=new gt.Item({props:{onSelect:e[11],$$slots:{default:[vn]},$$scope:{ctx:e}}}),a=new gt.Item({props:{onSelect:e[12],$$slots:{default:[xn]},$$scope:{ctx:e}}}),{c(){Y(t.$$.fragment),n=_(),Y(r.$$.fragment),o=_(),Y(a.$$.fragment)},m(i,s){E(t,i,s),I(i,n,s),E(r,i,s),I(i,o,s),E(a,i,s),c=!0},p(i,s){const u={};33554432&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u);const f={};33554432&s&&(f.$$scope={dirty:s,ctx:i}),r.$set(f);const l={};33554432&s&&(l.$$scope={dirty:s,ctx:i}),a.$set(l)},i(i){c||(M(t.$$.fragment,i),M(r.$$.fragment,i),M(a.$$.fragment,i),c=!0)},o(i){D(t.$$.fragment,i),D(r.$$.fragment,i),D(a.$$.fragment,i),c=!1},d(i){i&&(W(n),W(o)),j(t,i),j(r,i),j(a,i)}}}function Nn(e){let t,n,r,o;return t=new gt.Trigger({props:{$$slots:{default:[wn]},$$scope:{ctx:e}}}),r=new gt.Content({props:{$$slots:{default:[kn]},$$scope:{ctx:e}}}),{c(){Y(t.$$.fragment),n=_(),Y(r.$$.fragment)},m(a,c){E(t,a,c),I(a,n,c),E(r,a,c),o=!0},p(a,c){const i={};33554432&c&&(i.$$scope={dirty:c,ctx:a}),t.$set(i);const s={};33554432&c&&(s.$$scope={dirty:c,ctx:a}),r.$set(s)},i(a){o||(M(t.$$.fragment,a),M(r.$$.fragment,a),o=!0)},o(a){D(t.$$.fragment,a),D(r.$$.fragment,a),o=!1},d(a){a&&W(n),j(t,a),j(r,a)}}}function Mn(e){let t,n,r,o,a,c,i,s,u,f,l,d,h,m,p,w,P,q,v,k;n=new It({props:{occuredAt:e[0].occuredAt,requestID:e[0].requestId,pathName:e[0].pathName,repoRoot:e[0].repoRoot}});let A=tt(e[0].completions),x=[];for(let y=0;y<A.length;y+=1)x[y]=re(ne(e,A,y));const U=y=>D(x[y],1,1,()=>{x[y]=null});let S=e[1]&&oe(e);function F(y){e[14](y)}let et={rows:"3",placeholder:"Add feedback on this completion...",resize:"none"};return e[2].feedbackNote!==void 0&&(et.value=e[2].feedbackNote),c=new Ce({props:et}),me.push(()=>ge(c,"value",F)),f=new ht({props:{selected:e[2].selectedRating,rating:Q.negative,disabled:e[3],click:e[15]}}),d=new ht({props:{selected:e[2].selectedRating,rating:Q.positive,disabled:e[3],click:e[16]}}),P=new ht({props:{rating:Q.unset,click:e[17],disabled:e[3],hide:!(e[2].feedbackNote.trim().length>0)}}),{c(){t=T("div"),Y(n.$$.fragment),r=_();for(let y=0;y<x.length;y+=1)x[y].c();o=_(),S&&S.c(),a=_(),Y(c.$$.fragment),s=_(),u=T("div"),Y(f.$$.fragment),l=_(),Y(d.$$.fragment),h=_(),m=T("div"),p=H(e[4]),w=_(),Y(P.$$.fragment),N(m,"class","c-completion-item__thankyou svelte-157v4yj"),N(u,"class","c-completion-item__ratings svelte-157v4yj"),N(t,"class","c-completion-item svelte-157v4yj"),V(t,"c-completion-item--sending-feedback",e[3])},m(y,g){I(y,t,g),E(n,t,null),$(t,r);for(let R=0;R<x.length;R+=1)x[R]&&x[R].m(t,null);$(t,o),S&&S.m(t,null),$(t,a),E(c,t,null),$(t,s),$(t,u),E(f,u,null),$(u,l),E(d,u,null),$(u,h),$(u,m),$(m,p),$(u,w),E(P,u,null),q=!0,v||(k=G(window,"message",e[6]),v=!0)},p(y,[g]){const R={};if(1&g&&(R.occuredAt=y[0].occuredAt),1&g&&(R.requestID=y[0].requestId),1&g&&(R.pathName=y[0].pathName),1&g&&(R.repoRoot=y[0].repoRoot),n.$set(R),1&g){let B;for(A=tt(y[0].completions),B=0;B<A.length;B+=1){const mt=ne(y,A,B);x[B]?(x[B].p(mt,g),M(x[B],1)):(x[B]=re(mt),x[B].c(),M(x[B],1),x[B].m(t,o))}for(ot(),B=A.length;B<x.length;B+=1)U(B);at()}y[1]?S?(S.p(y,g),2&g&&M(S,1)):(S=oe(y),S.c(),M(S,1),S.m(t,a)):S&&(ot(),D(S,1,1,()=>{S=null}),at());const b={};!i&&4&g&&(i=!0,b.value=y[2].feedbackNote,he(()=>i=!1)),c.$set(b);const C={};4&g&&(C.selected=y[2].selectedRating),8&g&&(C.disabled=y[3]),f.$set(C);const L={};4&g&&(L.selected=y[2].selectedRating),8&g&&(L.disabled=y[3]),d.$set(L),(!q||16&g)&&X(p,y[4]);const ut={};8&g&&(ut.disabled=y[3]),4&g&&(ut.hide=!(y[2].feedbackNote.trim().length>0)),P.$set(ut),(!q||8&g)&&V(t,"c-completion-item--sending-feedback",y[3])},i(y){if(!q){M(n.$$.fragment,y);for(let g=0;g<A.length;g+=1)M(x[g]);M(S),M(c.$$.fragment,y),M(f.$$.fragment,y),M(d.$$.fragment,y),M(P.$$.fragment,y),q=!0}},o(y){D(n.$$.fragment,y),x=x.filter(Boolean);for(let g=0;g<x.length;g+=1)D(x[g]);D(S),D(c.$$.fragment,y),D(f.$$.fragment,y),D(d.$$.fragment,y),D(P.$$.fragment,y),q=!1},d(y){y&&W(t),j(n),qt(x,y),S&&S.d(),j(c),j(f),j(d),j(P),v=!1,k()}}}function Pn(e,t,n){let r,o,{completion:a}=t,{debug:c=!1}=t,{demo:i=!1}=t,s=dt.getFeedback(a.requestId),u="",f=!1,l="";function d(p){if(n(4,l=ve()),clearTimeout(r),r=setTimeout(()=>{n(4,l="")},4e3),o=s.selectedRating,p!==Q.unset&&n(2,s.selectedRating=p,s),i)return;let w=s.feedbackNote;u&&(w=`${s.feedbackNote} #${u}`),dt.setFeedback(a.requestId,s),n(3,f=!0),nt.postMessage({type:Z.completionRating,data:{requestId:a.requestId,rating:p,note:w.trim()}})}function h(p){u=p,m()}let m=()=>{};return e.$$set=p=>{"completion"in p&&n(0,a=p.completion),"debug"in p&&n(1,c=p.debug),"demo"in p&&n(9,i=p.demo)},[a,c,s,f,l,m,function(p){if(i)return;const w=p.data;switch(w.type){case Z.completionRatingDone:{const{requestId:P}=w.data;if(P!==a.requestId)return;n(3,f=!1),w.data.success||(n(2,s.selectedRating=o,s),dt.setFeedback(P,s));break}}},d,h,i,()=>h(""),()=>h("bad-formatting"),()=>h("hallucination"),function(p){m=p,n(5,m)},function(p){e.$$.not_equal(s.feedbackNote,p)&&(s.feedbackNote=p,n(2,s))},()=>d(Q.negative),()=>d(Q.positive),()=>d(Q.unset)]}class xe extends it{constructor(t){super(),st(this,t,Pn,Mn,ct,{completion:0,debug:1,demo:9})}}function qn(e){let t,n,r,o,a,c,i,s;return i=new xe({props:{completion:e[0],demo:!0}}),{c(){t=T("div"),n=T("div"),n.innerHTML=`<h2>History.</h2> <p>As you use Augment, we&#39;ll display the most recent suggestions here so you can tell us about
      any particularly good, or bad, suggestions.</p> <p>Below is an example of the information and feedback form we&#39;ll display for each suggestion.</p>`,r=_(),o=T("div"),a=_(),c=T("div"),Y(i.$$.fragment),N(n,"class","l-no-items__msg svelte-10bvc8"),N(o,"class","l-no-items__divider svelte-10bvc8"),N(c,"class","l-no-items__example svelte-10bvc8"),N(t,"class","l-no-items svelte-10bvc8")},m(u,f){I(u,t,f),$(t,n),$(t,r),$(t,o),$(t,a),$(t,c),E(i,c,null),s=!0},p:J,i(u){s||(M(i.$$.fragment,u),s=!0)},o(u){D(i.$$.fragment,u),s=!1},d(u){u&&W(t),j(i)}}}function Dn(e){return[{occuredAt:new Date,requestId:"12345678-1234-1234-1234-123456789123",repoRoot:"/home/<USER>/projects/example-project",pathName:"src/example.js",prefix:"co",completions:[{text:'nsole.log("Hello World.");',skippedSuffix:"",suffixReplacementText:""}],suffix:`

`}]}class Tn extends it{constructor(t){super(),st(this,t,Dn,qn,ct,{})}}function Rn(e){let t,n,r;return{c(){t=T("div"),t.textContent="Click to view diff",N(t,"class","c-instruction-item__no-modifications svelte-15p7ohn"),N(t,"role","button"),N(t,"tabindex","0")},m(o,a){I(o,t,a),n||(r=[G(t,"keyup",e[4]),G(t,"click",e[4])],n=!0)},p:J,i:J,o:J,d(o){o&&W(t),n=!1,$t(r)}}}function Sn(e){let t,n,r,o,a,c,i;r=new Dt({props:{options:Wt,text:e[3],pathName:e[0].pathName}});const s=[In,An],u=[];function f(l,d){return l[3]!==l[2]?0:1}return a=f(e),c=u[a]=s[a](e),{c(){t=T("section"),n=H(`original:
      `),Y(r.$$.fragment),o=_(),c.c()},m(l,d){I(l,t,d),$(t,n),E(r,t,null),$(t,o),u[a].m(t,null),i=!0},p(l,d){const h={};8&d&&(h.text=l[3]),1&d&&(h.pathName=l[0].pathName),r.$set(h);let m=a;a=f(l),a===m?u[a].p(l,d):(ot(),D(u[m],1,1,()=>{u[m]=null}),at(),c=u[a],c?c.p(l,d):(c=u[a]=s[a](l),c.c()),M(c,1),c.m(t,null))},i(l){i||(M(r.$$.fragment,l),M(c),i=!0)},o(l){D(r.$$.fragment,l),D(c),i=!1},d(l){l&&W(t),j(r),u[a].d()}}}function Cn(e){let t;return{c(){t=T("div"),t.textContent="No modification to original code",N(t,"class","c-instruction-item__no-modifications svelte-15p7ohn")},m(n,r){I(n,t,r)},p:J,i:J,o:J,d(n){n&&W(t)}}}function An(e){let t;return{c(){t=T("div"),t.textContent="No modification to original code",N(t,"class","c-instruction-item__no-modifications svelte-15p7ohn")},m(n,r){I(n,t,r)},p:J,i:J,o:J,d(n){n&&W(t)}}}function In(e){let t,n,r;return n=new Dt({props:{options:Wt,text:e[2],pathName:e[0].pathName}}),{c(){t=H(`modified:
        `),Y(n.$$.fragment)},m(o,a){I(o,t,a),E(n,o,a),r=!0},p(o,a){const c={};4&a&&(c.text=o[2]),1&a&&(c.pathName=o[0].pathName),n.$set(c)},i(o){r||(M(n.$$.fragment,o),r=!0)},o(o){D(n.$$.fragment,o),r=!1},d(o){o&&W(t),j(n,o)}}}function Wn(e){let t,n,r,o,a,c;n=new It({props:{occuredAt:e[0].occuredAt,requestID:e[0].requestId,pathName:e[0].pathName,repoRoot:e[0].repoRoot,prompt:e[0].prompt}});const i=[Cn,Sn,Rn],s=[];function u(f,l){return f[0].selectedText===f[0].modifiedText?0:f[0].userRequested||f[1]===f[0].requestId?1:2}return o=u(e),a=s[o]=i[o](e),{c(){t=T("div"),Y(n.$$.fragment),r=_(),a.c(),N(t,"class","c-instruction-item svelte-15p7ohn")},m(f,l){I(f,t,l),E(n,t,null),$(t,r),s[o].m(t,null),c=!0},p(f,[l]){const d={};1&l&&(d.occuredAt=f[0].occuredAt),1&l&&(d.requestID=f[0].requestId),1&l&&(d.pathName=f[0].pathName),1&l&&(d.repoRoot=f[0].repoRoot),1&l&&(d.prompt=f[0].prompt),n.$set(d);let h=o;o=u(f),o===h?s[o].p(f,l):(ot(),D(s[h],1,1,()=>{s[h]=null}),at(),a=s[o],a?a.p(f,l):(a=s[o]=i[o](f),a.c()),M(a,1),a.m(t,null))},i(f){c||(M(n.$$.fragment,f),M(a),c=!0)},o(f){D(n.$$.fragment,f),D(a),c=!1},d(f){f&&W(t),j(n),s[o].d()}}}function ae(e){const t=e.split(`
`);for(let n=t.length-1;n>=0;n--)if(t[n].trim().length>0)return t.slice(0,n+1).join(`
`);return""}function Fn(e,t,n){let r,o,a,{instruction:c}=t;return e.$$set=i=>{"instruction"in i&&n(0,c=i.instruction)},e.$$.update=()=>{1&e.$$.dirty&&n(3,r=ae(c.selectedText)),1&e.$$.dirty&&n(2,o=ae(c.modifiedText))},[c,a,o,r,function(){n(1,a=c.requestId)}]}class _n extends it{constructor(t){super(),st(this,t,Fn,Wn,ct,{instruction:0})}}function ie(e,t,n){const r=e.slice();return r[21]=t[n],r}function se(e,t,n){const r=e.slice();return r[21]=t[n],r}function ce(e){let t,n,r,o,a,c,i,s,u,f,l,d,h,m,p,w,P,q,v,k,A,x=e[21].qualifiedPathName.relPath+"",U=e[21].lineRange.start+(e[21].lineRange.start<e[21].lineRange.stop?1:0)+"",S=e[21].lineRange.stop+"",F=e[21].result.changeDescription+"";function et(){return e[11](e[21])}function y(){return e[12](e[21])}return w=new Dt({props:{text:e[21].result.existingCode,pathName:e[21].qualifiedPathName.relPath,options:{lineNumbers:"off"}}}),q=new Dt({props:{text:e[21].result.suggestedCode,pathName:e[21].qualifiedPathName.relPath,options:{lineNumbers:"off"}}}),{c(){t=T("div"),n=T("pre"),r=T("code"),o=T("span"),a=H(x),c=H(": "),i=H(U),s=H("-"),u=H(S),f=_(),l=T("div"),d=H(F),h=_(),m=T("section"),p=H(`original:
        `),Y(w.$$.fragment),P=H(`
        modified:
        `),Y(q.$$.fragment),N(o,"class","c-next-edit-addition svelte-5y5llu"),V(o,"c-next-edit-addition-clicked",e[1]===e[21]),N(n,"data-language","plaintext"),N(t,"class","c-completion-code-block"),N(t,"role","button"),N(t,"tabindex","0")},m(g,R){I(g,t,R),$(t,n),$(n,r),$(r,o),$(o,a),$(o,c),$(o,i),$(o,s),$(o,u),I(g,f,R),I(g,l,R),$(l,d),I(g,h,R),I(g,m,R),$(m,p),E(w,m,null),$(m,P),E(q,m,null),v=!0,k||(A=[G(t,"click",et),G(t,"keydown",function(){pe(pt("Enter",y))&&pt("Enter",y).apply(this,arguments)})],k=!0)},p(g,R){e=g,(!v||32&R)&&x!==(x=e[21].qualifiedPathName.relPath+"")&&X(a,x),(!v||32&R)&&U!==(U=e[21].lineRange.start+(e[21].lineRange.start<e[21].lineRange.stop?1:0)+"")&&X(i,U),(!v||32&R)&&S!==(S=e[21].lineRange.stop+"")&&X(u,S),(!v||34&R)&&V(o,"c-next-edit-addition-clicked",e[1]===e[21]),(!v||32&R)&&F!==(F=e[21].result.changeDescription+"")&&X(d,F);const b={};32&R&&(b.text=e[21].result.existingCode),32&R&&(b.pathName=e[21].qualifiedPathName.relPath),w.$set(b);const C={};32&R&&(C.text=e[21].result.suggestedCode),32&R&&(C.pathName=e[21].qualifiedPathName.relPath),q.$set(C)},i(g){v||(M(w.$$.fragment,g),M(q.$$.fragment,g),v=!0)},o(g){D(w.$$.fragment,g),D(q.$$.fragment,g),v=!1},d(g){g&&(W(t),W(f),W(l),W(h),W(m)),j(w),j(q),k=!1,$t(A)}}}function ue(e){let t,n,r,o,a,c,i,s,u,f,l,d,h=e[21].qualifiedPathName.relPath+"",m=e[21].lineRange.start+(e[21].lineRange.start<e[21].lineRange.stop?1:0)+"",p=e[21].lineRange.stop+"";function w(){return e[13](e[21])}function P(){return e[14](e[21])}return{c(){t=T("div"),n=T("pre"),r=T("code"),o=T("span"),a=H(h),c=H(": "),i=H(m),s=H("-"),u=H(p),f=_(),N(o,"class","c-next-edit-addition svelte-5y5llu"),V(o,"c-next-edit-addition-clicked",e[1]===e[21]),N(n,"data-language","plaintext"),N(n,"class","c-next-edit-addition svelte-5y5llu"),N(t,"class","c-completion-code-block"),N(t,"role","button"),N(t,"tabindex","0")},m(q,v){I(q,t,v),$(t,n),$(n,r),$(r,o),$(o,a),$(o,c),$(o,i),$(o,s),$(o,u),$(t,f),l||(d=[G(t,"click",w),G(t,"keydown",function(){pe(pt("Enter",P))&&pt("Enter",P).apply(this,arguments)})],l=!0)},p(q,v){e=q,64&v&&h!==(h=e[21].qualifiedPathName.relPath+"")&&X(a,h),64&v&&m!==(m=e[21].lineRange.start+(e[21].lineRange.start<e[21].lineRange.stop?1:0)+"")&&X(i,m),64&v&&p!==(p=e[21].lineRange.stop+"")&&X(u,p),66&v&&V(o,"c-next-edit-addition-clicked",e[1]===e[21])},d(q){q&&W(t),l=!1,$t(d)}}}function Yn(e){var R;let t,n,r,o,a,c,i,s,u,f,l,d,h,m,p,w,P,q,v,k,A,x,U;n=new It({props:{occuredAt:e[0].occurredAt,requestID:e[0].requestId,repoRoot:((R=e[0].qualifiedPathName)==null?void 0:R.rootPath)??"",others:[`Request type: ${e[0].mode}/${e[0].scope}`]}});let S=tt(e[5]),F=[];for(let b=0;b<S.length;b+=1)F[b]=ce(se(e,S,b));const et=b=>D(F[b],1,1,()=>{F[b]=null});let y=tt(e[6]),g=[];for(let b=0;b<y.length;b+=1)g[b]=ue(ie(e,y,b));return h=new ht({props:{selected:e[2].selectedRating,rating:Q.negative,disabled:e[3],click:e[15]}}),p=new ht({props:{selected:e[2].selectedRating,rating:Q.positive,disabled:e[3],click:e[16]}}),k=new ht({props:{rating:Q.unset,click:e[17],disabled:e[3],hide:!(e[2].feedbackNote.trim().length>0)}}),{c(){t=T("div"),Y(n.$$.fragment),r=_(),o=T("section");for(let b=0;b<F.length;b+=1)F[b].c();a=_(),c=T("div"),c.textContent="Unchanged locations:",i=_();for(let b=0;b<g.length;b+=1)g[b].c();s=_(),u=T("vscode-text-area"),l=_(),d=T("div"),Y(h.$$.fragment),m=_(),Y(p.$$.fragment),w=_(),P=T("div"),q=H(e[4]),v=_(),Y(k.$$.fragment),N(c,"class","c-next-edit-item__no-modifications svelte-5y5llu"),wt(u,"rows","3"),wt(u,"placeholder","Add feedback on these suggestions..."),wt(u,"resize","none"),wt(u,"value",f=e[2].feedbackNote),N(P,"class","c-completion-item__thankyou"),N(d,"class","c-completion-item__ratings"),N(t,"class","c-next-edit-item svelte-5y5llu")},m(b,C){I(b,t,C),E(n,t,null),$(t,r),$(t,o);for(let L=0;L<F.length;L+=1)F[L]&&F[L].m(o,null);$(o,a),$(o,c),$(o,i);for(let L=0;L<g.length;L+=1)g[L]&&g[L].m(o,null);$(t,s),$(t,u),$(t,l),$(t,d),E(h,d,null),$(d,m),E(p,d,null),$(d,w),$(d,P),$(P,q),$(d,v),E(k,d,null),A=!0,x||(U=[G(window,"message",e[7]),G(u,"input",e[10])],x=!0)},p(b,[C]){var Ft;const L={};if(1&C&&(L.occuredAt=b[0].occurredAt),1&C&&(L.requestID=b[0].requestId),1&C&&(L.repoRoot=((Ft=b[0].qualifiedPathName)==null?void 0:Ft.rootPath)??""),1&C&&(L.others=[`Request type: ${b[0].mode}/${b[0].scope}`]),n.$set(L),290&C){let z;for(S=tt(b[5]),z=0;z<S.length;z+=1){const bt=se(b,S,z);F[z]?(F[z].p(bt,C),M(F[z],1)):(F[z]=ce(bt),F[z].c(),M(F[z],1),F[z].m(o,a))}for(ot(),z=S.length;z<F.length;z+=1)et(z);at()}if(322&C){let z;for(y=tt(b[6]),z=0;z<y.length;z+=1){const bt=ie(b,y,z);g[z]?g[z].p(bt,C):(g[z]=ue(bt),g[z].c(),g[z].m(o,null))}for(;z<g.length;z+=1)g[z].d(1);g.length=y.length}(!A||4&C&&f!==(f=b[2].feedbackNote))&&wt(u,"value",f);const ut={};4&C&&(ut.selected=b[2].selectedRating),8&C&&(ut.disabled=b[3]),h.$set(ut);const B={};4&C&&(B.selected=b[2].selectedRating),8&C&&(B.disabled=b[3]),p.$set(B),(!A||16&C)&&X(q,b[4]);const mt={};8&C&&(mt.disabled=b[3]),4&C&&(mt.hide=!(b[2].feedbackNote.trim().length>0)),k.$set(mt)},i(b){if(!A){M(n.$$.fragment,b);for(let C=0;C<S.length;C+=1)M(F[C]);M(h.$$.fragment,b),M(p.$$.fragment,b),M(k.$$.fragment,b),A=!0}},o(b){D(n.$$.fragment,b),F=F.filter(Boolean);for(let C=0;C<F.length;C+=1)D(F[C]);D(h.$$.fragment,b),D(p.$$.fragment,b),D(k.$$.fragment,b),A=!1},d(b){b&&W(t),j(n),qt(F,b),qt(g,b),j(h),j(p),j(k),x=!1,$t(U)}}}function En(e,t,n){let r,o,{result:a}=t,c=null,i=dt.getFeedback(a.requestId),s=!1,u="";function f(m){nt.postMessage({type:Z.openFile,data:{repoRoot:m.qualifiedPathName.rootPath,pathName:m.result.path,range:m.lineRange,differentTab:!0}}),n(1,c=m)}function l(m){n(4,u=ve()),clearTimeout(r),r=setTimeout(()=>{n(4,u="")},4e3),o=i.selectedRating,m!==Q.unset&&n(2,i.selectedRating=m,i);let p=i.feedbackNote;dt.setFeedback(a.requestId,i),n(3,s=!0),nt.postMessage({type:Z.nextEditRating,data:{requestId:a.requestId,rating:m,note:p.trim()}})}let d=[],h=[];return e.$$set=m=>{"result"in m&&n(0,a=m.result)},e.$$.update=()=>{1&e.$$.dirty&&(n(5,d=a.suggestions.filter(m=>m.changeType!==Yt.noop)),n(6,h=a.suggestions.filter(m=>m.changeType===Yt.noop)))},[a,c,i,s,u,d,h,function(m){const p=m.data;switch(p.type){case Z.nextEditRatingDone:{const{requestId:w}=p.data;if(w!==a.requestId)return;n(3,s=!1),p.data.success||(n(2,i.selectedRating=o,i),dt.setFeedback(w,i));break}}},f,l,function(m){const p=m.target;n(2,i.feedbackNote=p.value,i)},m=>f(m),m=>f(m),m=>f(m),m=>f(m),()=>l(Q.negative),()=>l(Q.positive),()=>l(Q.unset)]}class jn extends it{constructor(t){super(),st(this,t,En,Yn,ct,{result:0})}}function le(e,t,n){const r=e.slice();return r[9]=t[n],r}function On(e){let t,n,r=[],o=new Map,a=tt(e[1]);const c=i=>i[9].requestId;for(let i=0;i<a.length;i+=1){let s=le(e,a,i),u=c(s);o.set(u,r[i]=de(u,s))}return{c(){for(let i=0;i<r.length;i+=1)r[i].c();t=$e()},m(i,s){for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(i,s);I(i,t,s),n=!0},p(i,s){3&s&&(a=tt(i[1]),ot(),r=Pe(r,s,c,1,i,a,o,t.parentNode,qe,de,t,le),at())},i(i){if(!n){for(let s=0;s<a.length;s+=1)M(r[s]);n=!0}},o(i){for(let s=0;s<r.length;s+=1)D(r[s]);n=!1},d(i){i&&W(t);for(let s=0;s<r.length;s+=1)r[s].d(i)}}}function Hn(e){let t,n;return t=new Tn({}),{c(){Y(t.$$.fragment)},m(r,o){E(t,r,o),n=!0},p:J,i(r){n||(M(t.$$.fragment,r),n=!0)},o(r){D(t.$$.fragment,r),n=!1},d(r){j(t,r)}}}function zn(e){let t,n;return t=new jn({props:{result:e[9].result}}),{c(){Y(t.$$.fragment)},m(r,o){E(t,r,o),n=!0},p(r,o){const a={};2&o&&(a.result=r[9].result),t.$set(a)},i(r){n||(M(t.$$.fragment,r),n=!0)},o(r){D(t.$$.fragment,r),n=!1},d(r){j(t,r)}}}function Ln(e){let t,n;return t=new _n({props:{instruction:fe(e[9])}}),{c(){Y(t.$$.fragment)},m(r,o){E(t,r,o),n=!0},p(r,o){const a={};2&o&&(a.instruction=fe(r[9])),t.$set(a)},i(r){n||(M(t.$$.fragment,r),n=!0)},o(r){D(t.$$.fragment,r),n=!1},d(r){j(t,r)}}}function Bn(e){var r,o;let t,n;return t=new xe({props:{debug:((r=e[0])==null?void 0:r.enableDebugFeatures)||((o=e[0])==null?void 0:o.enableReviewerWorkflows),completion:e[9]}}),{c(){Y(t.$$.fragment)},m(a,c){E(t,a,c),n=!0},p(a,c){var s,u;const i={};1&c&&(i.debug=((s=a[0])==null?void 0:s.enableDebugFeatures)||((u=a[0])==null?void 0:u.enableReviewerWorkflows)),2&c&&(i.completion=a[9]),t.$set(i)},i(a){n||(M(t.$$.fragment,a),n=!0)},o(a){D(t.$$.fragment,a),n=!1},d(a){j(t,a)}}}function de(e,t){let n,r,o,a,c,i;const s=[Bn,Ln,zn],u=[];function f(l,d){return"completions"in l[9]?0:"prompt"in l[9]?1:"result"in l[9]?2:-1}return~(r=f(t))&&(o=u[r]=s[r](t)),{key:e,first:null,c(){n=$e(),o&&o.c(),a=_(),c=T("div"),N(c,"class","l-items-list__divider svelte-1ed3e6r"),this.first=n},m(l,d){I(l,n,d),~r&&u[r].m(l,d),I(l,a,d),I(l,c,d),i=!0},p(l,d){let h=r;r=f(t=l),r===h?~r&&u[r].p(t,d):(o&&(ot(),D(u[h],1,1,()=>{u[h]=null}),at()),~r?(o=u[r],o?o.p(t,d):(o=u[r]=s[r](t),o.c()),M(o,1),o.m(a.parentNode,a)):o=null)},i(l){i||(M(o),i=!0)},o(l){D(o),i=!1},d(l){l&&(W(n),W(a),W(c)),~r&&u[r].d(l)}}}function Qn(e){let t,n,r,o;const a=[Hn,On],c=[];function i(s,u){return s[1].length?1:0}return n=i(e),r=c[n]=a[n](e),{c(){t=T("main"),r.c(),N(t,"class","l-items-list svelte-1ed3e6r")},m(s,u){I(s,t,u),c[n].m(t,null),o=!0},p(s,u){let f=n;n=i(s),n===f?c[n].p(s,u):(ot(),D(c[f],1,1,()=>{c[f]=null}),at(),r=c[n],r?r.p(s,u):(r=c[n]=a[n](s),r.c()),M(r,1),r.m(t,null))},i(s){o||(M(r),o=!0)},o(s){D(r),o=!1},d(s){s&&W(t),c[n].d()}}}function Un(e){let t,n,r,o;return t=new Re.Root({props:{$$slots:{default:[Qn]},$$scope:{ctx:e}}}),{c(){Y(t.$$.fragment)},m(a,c){E(t,a,c),n=!0,r||(o=G(window,"message",e[2]),r=!0)},p(a,[c]){const i={};4099&c&&(i.$$scope={dirty:c,ctx:a}),t.$set(i)},i(a){n||(M(t.$$.fragment,a),n=!0)},o(a){D(t.$$.fragment,a),n=!1},d(a){j(t,a),r=!1,o()}}}function fe(e){if(!("prompt"in e))throw new Error("wrong type");if("completions"in e)throw new Error("wrong type");return e}function Gn(e,t,n){let r,o={},a={},c={};function i(l){for(const d of l)o[d.requestId]||n(3,o[d.requestId]={...d,occuredAt:Ct(d.occuredAt)},o);dt.cleanupFeedback(o)}function s(l){for(const d of l)if(!a[d.requestId]){if(typeof d.occuredAt=="string"){const h=d.occuredAt;d.occuredAt=Ct(h)}n(4,a[d.requestId]={...d},a)}}function u(l){for(const d of l)d.suggestions.length!==0&&n(5,c[d.requestId]={requestId:d.requestId,occuredAt:Ct(d.occurredAt),result:d},c)}nt.postMessage({type:Z.historyLoaded});let f=[];return e.$$.update=()=>{56&e.$$.dirty&&n(1,f=[...Object.values(a),...Object.values(o),...Object.values(c)].sort((l,d)=>d.occuredAt.getTime()-l.occuredAt.getTime()))},[r,f,function(l){const d=l.data;switch(d.type){case Z.historyInitialize:n(0,r=d.data.config),s(d.data.instructions),i(d.data.completionRequests),u(d.data.nextEdits);break;case Z.completions:i(d.data);break;case Z.instructions:s(d.data);break;case Z.nextEditSuggestions:u([d.data]);break;case Z.historyConfig:n(0,r=d.data)}},o,a,c]}new class extends it{constructor(e){super(),st(this,e,Gn,Un,ct,{})}}({target:document.getElementById("app")});
