import{c as i,a as o,b as t,s as m}from"./chunk-T2TOU4HS-isP4_W_q.js";import{_ as p}from"./AugmentMessage-DtUuYhLr.js";import"./chunk-5HRBRIJM-DP5crzIQ.js";import"./SpinnerAugment-BEPEN2tu.js";import"./CalloutAugment-DzngRWi1.js";import"./TextTooltipAugment-DAVq7Vla.js";import"./BaseButton-DBHsDlhs.js";import"./IconButtonAugment-DT9AU8SC.js";import"./Content-Bm7C6iJ1.js";import"./globals-D0QH3NT1.js";import"./arrow-up-right-from-square-Yo0BPgM2.js";import"./types-DK4HA_lx.js";import"./file-paths-BcSg4gks.js";import"./folder-DhY9vbAU.js";import"./github-kew-evZ8.js";import"./folder-opened-BLAu3pyz.js";import"./types-CGlLNakm.js";import"./file-type-utils-B3gunxPI.js";import"./check-56hMuF8e.js";import"./types-DDm27S8B.js";import"./index-CiMDylqQ.js";import"./utils-Rh_q5w_c.js";import"./ra-diff-ops-model-pDvb0wIq.js";import"./index-DY0Q9XhW.js";import"./CardAugment-RCmwRtRa.js";import"./isObjectLike-C4kUqRHQ.js";import"./TextAreaAugment-C1Wf9cvH.js";import"./diff-utils-CHDUdEQq.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-oyQWDnzB.js";import"./keypress-DD1aQVr0.js";import"./await_block-DO_bDmS_.js";import"./CollapseButtonAugment-CEo2LYfW.js";import"./ButtonAugment-xh-SOBaV.js";import"./MaterialIcon-BGa1zPPN.js";import"./CopyButton-BVbGAvmE.js";import"./magnifying-glass-BmBw0jRN.js";import"./ellipsis-DsAFryUF.js";import"./IconFilePath-BCQiczSW.js";import"./LanguageIcon-CV8JBzL9.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-CId0aUQb.js";import"./lodash-CujEqTm9.js";import"./mcp-logo-C2xFPiFq.js";import"./terminal-CS_V-nAg.js";import"./pen-to-square-DEUxz3in.js";import"./augment-logo-DTgokSKV.js";var rr={parser:i,db:o,renderer:t,styles:m,init:p(r=>{r.class||(r.class={}),r.class.arrowMarkerAbsolute=r.arrowMarkerAbsolute,o.clear()},"init")};export{rr as diagram};
