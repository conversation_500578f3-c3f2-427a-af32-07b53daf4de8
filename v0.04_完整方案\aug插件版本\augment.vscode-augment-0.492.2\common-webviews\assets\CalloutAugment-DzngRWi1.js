import{S,i as A,s as L,T as M,F as y,a as g,G as v,I as O,J as N,K as h,e as d,L as P,g as Q,u,t as $,h as f,M as R,N as b,O as T,j as V,P as w,Q as W,c as F,q as k,r as B,R as G,V as I,W as J}from"./SpinnerAugment-BEPEN2tu.js";const D=o=>({}),j=o=>({});function q(o){let a,c;const i=o[8].icon,l=w(i,o,o[9],j);return{c(){a=v("div"),l&&l.c(),F(a,"class","c-callout-icon svelte-8as1i4")},m(e,n){d(e,a,n),l&&l.m(a,null),c=!0},p(e,n){l&&l.p&&(!c||512&n)&&G(l,i,e,e[9],c?J(i,e[9],n,D):I(e[9]),j)},i(e){c||(u(l,e),c=!0)},o(e){$(l,e),c=!1},d(e){e&&f(a),l&&l.d(e)}}}function E(o){let a,c,i,l=o[7].icon&&q(o);const e=o[8].default,n=w(e,o,o[9],null);return{c(){l&&l.c(),a=W(),c=v("div"),n&&n.c(),F(c,"class","c-callout-body")},m(t,s){l&&l.m(t,s),d(t,a,s),d(t,c,s),n&&n.m(c,null),i=!0},p(t,s){t[7].icon?l?(l.p(t,s),128&s&&u(l,1)):(l=q(t),l.c(),u(l,1),l.m(a.parentNode,a)):l&&(k(),$(l,1,1,()=>{l=null}),B()),n&&n.p&&(!i||512&s)&&G(n,e,t,t[9],i?J(e,t[9],s,null):I(t[9]),null)},i(t){i||(u(l),u(n,t),i=!0)},o(t){$(l),$(n,t),i=!1},d(t){t&&(f(a),f(c)),l&&l.d(t),n&&n.d(t)}}}function H(o){let a,c,i,l;c=new M({props:{size:o[6],$$slots:{default:[E]},$$scope:{ctx:o}}});let e=[y(o[0]),{class:i=`c-callout c-callout--${o[0]} c-callout--${o[1]} c-callout--size-${o[2]} ${o[5]}`},o[4]],n={};for(let t=0;t<e.length;t+=1)n=g(n,e[t]);return{c(){a=v("div"),O(c.$$.fragment),N(a,n),h(a,"c-callout--highContrast",o[3]),h(a,"svelte-8as1i4",!0)},m(t,s){d(t,a,s),P(c,a,null),l=!0},p(t,[s]){const p={};640&s&&(p.$$scope={dirty:s,ctx:t}),c.$set(p),N(a,n=Q(e,[1&s&&y(t[0]),(!l||39&s&&i!==(i=`c-callout c-callout--${t[0]} c-callout--${t[1]} c-callout--size-${t[2]} ${t[5]}`))&&{class:i},16&s&&t[4]])),h(a,"c-callout--highContrast",t[3]),h(a,"svelte-8as1i4",!0)},i(t){l||(u(c.$$.fragment,t),l=!0)},o(t){$(c.$$.fragment,t),l=!1},d(t){t&&f(a),R(c)}}}function U(o,a,c){let i,l;const e=["color","variant","size","highContrast"];let n=b(a,e),{$$slots:t={},$$scope:s}=a;const p=T(t);let{color:z="info"}=a,{variant:C="soft"}=a,{size:m=2}=a,{highContrast:x=!1}=a;const K=m;return o.$$set=r=>{a=g(g({},a),V(r)),c(10,n=b(a,e)),"color"in r&&c(0,z=r.color),"variant"in r&&c(1,C=r.variant),"size"in r&&c(2,m=r.size),"highContrast"in r&&c(3,x=r.highContrast),"$$scope"in r&&c(9,s=r.$$scope)},o.$$.update=()=>{c(5,{class:i,...l}=n,i,(c(4,l),c(10,n)))},[z,C,m,x,l,i,K,p,t,s]}class Y extends S{constructor(a){super(),A(this,a,U,H,L,{color:0,variant:1,size:2,highContrast:3})}}export{Y as C};
