import{S as P,i as V,s as Q,a as J,b as ye,H as be,w as Ce,x as Me,y as Re,h as y,d as se,z as Le,g as Se,n as M,j as oe,a9 as ue,e as b,u as f,q as D,t as m,r as U,ag as _,a3 as de,ah as Fe,A as H,_ as G,I as h,L as v,M as w,$ as B,a1 as k,X as O,Q as W,G as F,c as R,f as z,ae as Ge,T as fe,Z as me,a5 as Ee,a2 as Te}from"./SpinnerAugment-BEPEN2tu.js";import"./design-system-init-BG5cEFim.js";import{h as A,W as K,e as re}from"./BaseButton-DBHsDlhs.js";import{S as ze,O as Ie}from"./OpenFileButton-CBRUZTYt.js";import{C as pe,E as ge}from"./chat-flags-model-CRX90O9_.js";import{M as Y,T as he}from"./TextTooltipAugment-DAVq7Vla.js";import{M as Be}from"./MarkdownEditor-CsUMWp3w.js";import{M as Z,b as ke}from"./types-DK4HA_lx.js";import{D as N}from"./index-DY0Q9XhW.js";import{B as ve}from"./ButtonAugment-xh-SOBaV.js";import{C as we}from"./lodash-CujEqTm9.js";import{F as Ae}from"./Filespan-CId0aUQb.js";import{T as xe}from"./Content-Bm7C6iJ1.js";import"./check-56hMuF8e.js";import"./types-DDm27S8B.js";import"./index-CiMDylqQ.js";import"./arrow-up-right-from-square-Yo0BPgM2.js";import"./file-paths-BcSg4gks.js";import"./utils-Rh_q5w_c.js";import"./ra-diff-ops-model-pDvb0wIq.js";import"./types-CGlLNakm.js";import"./IconButtonAugment-DT9AU8SC.js";import"./TextAreaAugment-C1Wf9cvH.js";import"./CardAugment-RCmwRtRa.js";import"./globals-D0QH3NT1.js";function Ne(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},r[0]],s={};for(let o=0;o<t.length;o+=1)s=J(s,t[o]);return{c(){e=ye("svg"),n=new be(!0),this.h()},l(o){e=Ce(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Me(e);n=Re(i,!0),i.forEach(y),this.h()},h(){n.a=null,se(e,s)},m(o,i){Le(o,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',e)},p(o,[i]){se(e,s=Se(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&o[0]]))},i:M,o:M,d(o){o&&y(e)}}}function Oe(r,e,n){return r.$$set=t=>{n(0,e=J(J({},e),oe(t)))},[e=oe(e)]}class ee extends P{constructor(e){super(),V(this,e,Oe,Ne,Q,{})}}function ae(r,e,n){const t=r.slice();return t[26]=e[n],t[28]=n,t}function ie(r){let e,n,t,s;const o=[_e,qe],i=[];function l(a,$){return a[0]?0:1}return e=l(r),n=i[e]=o[e](r),{c(){n.c(),t=ue()},m(a,$){i[e].m(a,$),b(a,t,$),s=!0},p(a,$){let c=e;e=l(a),e===c?i[e].p(a,$):(D(),m(i[c],1,1,()=>{i[c]=null}),U(),n=i[e],n?n.p(a,$):(n=i[e]=o[e](a),n.c()),f(n,1),n.m(t.parentNode,t))},i(a){s||(f(n),s=!0)},o(a){m(n),s=!1},d(a){a&&y(t),i[e].d(a)}}}function qe(r){let e,n,t={content:r[5],triggerOn:[xe.Hover],side:"top",$$slots:{default:[We]},$$scope:{ctx:r}};return e=new he({props:t}),r[21](e),{c(){h(e.$$.fragment)},m(s,o){v(e,s,o),n=!0},p(s,o){const i={};32&o&&(i.content=s[5]),536870912&o&&(i.$$scope={dirty:o,ctx:s}),e.$set(i)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){r[21](null),w(e,s)}}}function _e(r){let e,n,t,s;function o(a){r[19](a)}function i(a){r[20](a)}let l={$$slots:{default:[et]},$$scope:{ctx:r}};return r[3]!==void 0&&(l.requestClose=r[3]),r[2]!==void 0&&(l.focusedIndex=r[2]),e=new N.Root({props:l}),G.push(()=>B(e,"requestClose",o)),G.push(()=>B(e,"focusedIndex",i)),{c(){h(e.$$.fragment)},m(a,$){v(e,a,$),s=!0},p(a,$){const c={};536871794&$&&(c.$$scope={dirty:$,ctx:a}),!n&&8&$&&(n=!0,c.requestClose=a[3],k(()=>n=!1)),!t&&4&$&&(t=!0,c.focusedIndex=a[2],k(()=>t=!1)),e.$set(c)},i(a){s||(f(e.$$.fragment,a),s=!0)},o(a){m(e.$$.fragment,a),s=!1},d(a){w(e,a)}}}function He(r){let e;return{c(){e=O("Rules")},m(n,t){b(n,e,t)},d(n){n&&y(e)}}}function De(r){let e,n;return e=new ee({props:{slot:"iconLeft"}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p:M,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function Ue(r){let e,n;return e=new we({props:{slot:"iconRight"}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p:M,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function We(r){let e,n;return e=new ve({props:{color:"neutral",variant:"soft",size:1,disabled:!0,$$slots:{iconRight:[Ue],iconLeft:[De],default:[He]},$$scope:{ctx:r}}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const o={};536870912&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function je(r){let e,n=(r[8]?r[8].path:"Rules")+"";return{c(){e=O(n)},m(t,s){b(t,e,s)},p(t,s){256&s&&n!==(n=(t[8]?t[8].path:"Rules")+"")&&me(e,n)},d(t){t&&y(e)}}}function Pe(r){let e,n;return e=new ee({props:{slot:"iconLeft"}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p:M,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function Ve(r){let e,n;return e=new we({props:{slot:"iconRight"}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p:M,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function Qe(r){let e,n;return e=new ve({props:{color:"neutral",variant:"soft",size:1,disabled:r[6],$$slots:{iconRight:[Ve],iconLeft:[Pe],default:[je]},$$scope:{ctx:r}}}),e.$on("click",r[14]),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const o={};64&s&&(o.disabled=t[6]),536871168&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function Xe(r){let e,n,t={content:r[5],triggerOn:[xe.Hover],side:"top",$$slots:{default:[Qe]},$$scope:{ctx:r}};return e=new he({props:t}),r[17](e),{c(){h(e.$$.fragment)},m(s,o){v(e,s,o),n=!0},p(s,o){const i={};32&o&&(i.content=s[5]),536871232&o&&(i.$$scope={dirty:o,ctx:s}),e.$set(i)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){r[17](null),w(e,s)}}}function Ze(r){let e,n;return e=new Ae({props:{filepath:r[26].path}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const o={};2&s&&(o.filepath=t[26].path),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function ce(r){let e,n;function t(){return r[18](r[26])}return e=new N.Item({props:{onSelect:t,highlight:r[9]===r[28],$$slots:{default:[Ze]},$$scope:{ctx:r}}}),{c(){h(e.$$.fragment)},m(s,o){v(e,s,o),n=!0},p(s,o){r=s;const i={};2&o&&(i.onSelect=t),512&o&&(i.highlight=r[9]===r[28]),536870914&o&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(s){n||(f(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){w(e,s)}}}function le(r){let e,n,t,s;return e=new N.Separator({}),t=new N.Label({props:{$$slots:{default:[Ke]},$$scope:{ctx:r}}}),{c(){h(e.$$.fragment),n=W(),h(t.$$.fragment)},m(o,i){v(e,o,i),b(o,n,i),v(t,o,i),s=!0},p(o,i){const l={};536871426&i&&(l.$$scope={dirty:i,ctx:o}),t.$set(l)},i(o){s||(f(e.$$.fragment,o),f(t.$$.fragment,o),s=!0)},o(o){m(e.$$.fragment,o),m(t.$$.fragment,o),s=!1},d(o){o&&y(n),w(e,o),w(t,o)}}}function Je(r){let e,n=$e(r[1][r[9]])+"";return{c(){e=O(n)},m(t,s){b(t,e,s)},p(t,s){514&s&&n!==(n=$e(t[1][t[9]])+"")&&me(e,n)},d(t){t&&y(e)}}}function Ke(r){let e,n;return e=new fe({props:{size:1,color:"neutral",$$slots:{default:[Je]},$$scope:{ctx:r}}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const o={};536871426&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function Ye(r){let e,n,t,s=re(r[1]),o=[];for(let a=0;a<s.length;a+=1)o[a]=ce(ae(r,s,a));const i=a=>m(o[a],1,1,()=>{o[a]=null});let l=r[9]!==void 0&&r[1][r[9]]&&le(r);return{c(){e=F("div");for(let a=0;a<o.length;a+=1)o[a].c();n=W(),l&&l.c(),R(e,"class","rules-dropdown-content svelte-18wohv")},m(a,$){b(a,e,$);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(e,null);z(e,n),l&&l.m(e,null),t=!0},p(a,$){if(8706&$){let c;for(s=re(a[1]),c=0;c<s.length;c+=1){const g=ae(a,s,c);o[c]?(o[c].p(g,$),f(o[c],1)):(o[c]=ce(g),o[c].c(),f(o[c],1),o[c].m(e,n))}for(D(),c=s.length;c<o.length;c+=1)i(c);U()}a[9]!==void 0&&a[1][a[9]]?l?(l.p(a,$),514&$&&f(l,1)):(l=le(a),l.c(),f(l,1),l.m(e,null)):l&&(D(),m(l,1,1,()=>{l=null}),U())},i(a){if(!t){for(let $=0;$<s.length;$+=1)f(o[$]);f(l),t=!0}},o(a){o=o.filter(Boolean);for(let $=0;$<o.length;$+=1)m(o[$]);m(l),t=!1},d(a){a&&y(e),Ge(o,a),l&&l.d()}}}function et(r){let e,n,t,s;return e=new N.Trigger({props:{$$slots:{default:[Xe]},$$scope:{ctx:r}}}),t=new N.Content({props:{side:"bottom",align:"start",$$slots:{default:[Ye]},$$scope:{ctx:r}}}),{c(){h(e.$$.fragment),n=W(),h(t.$$.fragment)},m(o,i){v(e,o,i),b(o,n,i),v(t,o,i),s=!0},p(o,i){const l={};536871280&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l);const a={};536871426&i&&(a.$$scope={dirty:i,ctx:o}),t.$set(a)},i(o){s||(f(e.$$.fragment,o),f(t.$$.fragment,o),s=!0)},o(o){m(e.$$.fragment,o),m(t.$$.fragment,o),s=!1},d(o){o&&y(n),w(e,o),w(t,o)}}}function tt(r){let e,n,t=!r[7]&&ie(r);return{c(){t&&t.c(),e=ue()},m(s,o){t&&t.m(s,o),b(s,e,o),n=!0},p(s,[o]){s[7]?t&&(D(),m(t,1,1,()=>{t=null}),U()):t?(t.p(s,o),128&o&&f(t,1)):(t=ie(s),t.c(),f(t,1),t.m(e.parentNode,e))},i(s){n||(f(t),n=!0)},o(s){m(t),n=!1},d(s){s&&y(e),t&&t.d(s)}}}function $e(r){return`Move to ${r.path}`}function nt(r,e,n){let t,s,o,i,l,a,$,c=M,g=()=>(c(),c=Fe(q,p=>n(9,$=p)),q);r.$$.on_destroy.push(()=>c());let{onRuleSelected:u}=e,{disabled:d=!1}=e;const x=new Y(A),L=new pe,C=new ge(A,x,L),S=H([]);_(r,S,p=>n(1,i=p));const E=H(!0);_(r,E,p=>n(7,l=p));const X=H(void 0);let q;_(r,X,p=>n(8,a=p)),g();let T,j=()=>{};function te(p){X.set(p),u(p),j()}return de(()=>{(async function(){try{E.set(!0);const I=await C.findRules("",100);S.set(I)}catch(I){console.error("Failed to load rules:",I),S.set([])}finally{E.set(!1)}})();const p=I=>{var ne;((ne=I.data)==null?void 0:ne.type)===K.getRulesListResponse&&(S.set(I.data.data||[]),E.set(!1))};return window.addEventListener("message",p),()=>{window.removeEventListener("message",p)}}),r.$$set=p=>{"onRuleSelected"in p&&n(15,u=p.onRuleSelected),"disabled"in p&&n(16,d=p.disabled)},r.$$.update=()=>{2&r.$$.dirty&&n(0,t=i.length>0),65537&r.$$.dirty&&n(6,s=d||!t),1&r.$$.dirty&&n(5,o=t?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")},[t,i,q,j,T,o,s,l,a,$,S,E,X,te,function(){T&&T.requestClose()},u,d,function(p){G[p?"unshift":"push"](()=>{T=p,n(4,T)})},p=>te(p),function(p){j=p,n(3,j)},function(p){q=p,g(n(2,q))},function(p){G[p?"unshift":"push"](()=>{T=p,n(4,T)})}]}class st extends P{constructor(e){super(),V(this,e,nt,tt,Q,{onRuleSelected:15,disabled:16})}}function ot(r){let e;return{c(){e=O("User Guidelines")},m(n,t){b(n,e,t)},d(n){n&&y(e)}}}function rt(r){let e,n,t;return n=new ee({}),{c(){e=F("div"),h(n.$$.fragment),R(e,"slot","iconLeft"),R(e,"class","c-move-text-btn__left_icon svelte-1yddhs6")},m(s,o){b(s,e,o),v(n,e,null),t=!0},p:M,i(s){t||(f(n.$$.fragment,s),t=!0)},o(s){m(n.$$.fragment,s),t=!1},d(s){s&&y(e),w(n)}}}function at(r){let e;return{c(){e=O("Augment-Memories.md")},m(n,t){b(n,e,t)},d(n){n&&y(e)}}}function it(r){let e,n;return e=new fe({props:{slot:"text",size:1,$$slots:{default:[at]},$$scope:{ctx:r}}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const o={};131072&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function ct(r){let e,n,t,s,o,i,l,a,$,c,g;return s=new ze({props:{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:r[9],disabled:!r[2],stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,$$slots:{iconLeft:[rt],default:[ot]},$$scope:{ctx:r}}}),l=new st({props:{onRuleSelected:r[8],disabled:!r[2]}}),c=new Ie({props:{size:1,path:r[1],variant:"soft",onOpenLocalFile:r[10],$$slots:{text:[it]},$$scope:{ctx:r}}}),{c(){e=F("div"),n=F("div"),t=F("div"),h(s.$$.fragment),o=W(),i=F("div"),h(l.$$.fragment),a=W(),$=F("div"),h(c.$$.fragment),R(t,"class","c-move-text-btn svelte-1yddhs6"),R(i,"class","c-move-text-btn svelte-1yddhs6"),R(n,"class","l-file-controls-left svelte-1yddhs6"),R($,"class","l-file-controls-right svelte-1yddhs6"),R(e,"class","l-file-controls svelte-1yddhs6"),R(e,"slot","header")},m(u,d){b(u,e,d),z(e,n),z(n,t),v(s,t,null),z(n,o),z(n,i),v(l,i,null),z(e,a),z(e,$),v(c,$,null),g=!0},p(u,d){const x={};4&d&&(x.disabled=!u[2]),131072&d&&(x.$$scope={dirty:d,ctx:u}),s.$set(x);const L={};4&d&&(L.disabled=!u[2]),l.$set(L);const C={};2&d&&(C.path=u[1]),2&d&&(C.onOpenLocalFile=u[10]),131072&d&&(C.$$scope={dirty:d,ctx:u}),c.$set(C)},i(u){g||(f(s.$$.fragment,u),f(l.$$.fragment,u),f(c.$$.fragment,u),g=!0)},o(u){m(s.$$.fragment,u),m(l.$$.fragment,u),m(c.$$.fragment,u),g=!1},d(u){u&&y(e),w(s),w(l),w(c)}}}function lt(r){let e,n,t,s,o,i;function l(u){r[11](u)}function a(u){r[12](u)}function $(u){r[13](u)}function c(u){r[14](u)}let g={saveFunction:r[6],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[ct]},$$scope:{ctx:r}};return r[2]!==void 0&&(g.selectedText=r[2]),r[3]!==void 0&&(g.selectionStart=r[3]),r[4]!==void 0&&(g.selectionEnd=r[4]),r[0]!==void 0&&(g.value=r[0]),e=new Be({props:g}),G.push(()=>B(e,"selectedText",l)),G.push(()=>B(e,"selectionStart",a)),G.push(()=>B(e,"selectionEnd",$)),G.push(()=>B(e,"value",c)),{c(){h(e.$$.fragment)},m(u,d){v(e,u,d),i=!0},p(u,[d]){const x={};131078&d&&(x.$$scope={dirty:d,ctx:u}),!n&&4&d&&(n=!0,x.selectedText=u[2],k(()=>n=!1)),!t&&8&d&&(t=!0,x.selectionStart=u[3],k(()=>t=!1)),!s&&16&d&&(s=!0,x.selectionEnd=u[4],k(()=>s=!1)),!o&&1&d&&(o=!0,x.value=u[0],k(()=>o=!1)),e.$set(x)},i(u){i||(f(e.$$.fragment,u),i=!0)},o(u){m(e.$$.fragment,u),i=!1},d(u){w(e,u)}}}function $t(r,e,n){let{text:t}=e,{path:s}=e;const o=new Y(A),i=new pe,l=new ge(A,o,i);let a="",$=0,c=0;const g=async()=>{s&&l.saveFile({repoRoot:"",pathName:s,content:t})};async function u(d){if(!a)return;let x,L,C;const S=a.slice(0,20);if(d==="userGuidelines"?(x="Move Content to User Guidelines",L=`Are you sure you want to move the selected content "${S}" to your user guidelines?`,C=Z.userGuidelines):d==="augmentGuidelines"?(x="Move Content to Workspace Guidelines",L=`Are you sure you want to move the selected content "${S}" to workspace guidelines?`,C=Z.augmentGuidelines):(x="Move Content to Rule",L=`Are you sure you want to move the selected content "${S}" to rule file "${d.rule.path}"?`,C=Z.rules),!await l.openConfirmationModal({title:x,message:L,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;d==="userGuidelines"?l.updateUserGuidelines(a):d==="augmentGuidelines"?l.updateWorkspaceGuidelines(a):l.updateRuleFile(d.rule.path,a);const E=t.substring(0,$)+t.substring(c);return n(0,t=E),await g(),l.reportAgentSessionEvent({eventName:ke.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:C}}}),"success"}return r.$$set=d=>{"text"in d&&n(0,t=d.text),"path"in d&&n(1,s=d.path)},[t,s,a,$,c,l,g,u,async function(d){await u({type:"rule",rule:d})},()=>u("userGuidelines"),async()=>(l.openFile({repoRoot:"",pathName:s}),"success"),function(d){a=d,n(2,a)},function(d){$=d,n(3,$)},function(d){c=d,n(4,c)},function(d){t=d,n(0,t)}]}class ut extends P{constructor(e){super(),V(this,e,$t,lt,Q,{text:0,path:1})}}function dt(r){let e;return{c(){e=O("Loading memories...")},m(n,t){b(n,e,t)},p:M,i:M,o:M,d(n){n&&y(e)}}}function ft(r){let e,n;return e=new ut({props:{text:r[0],path:r[1]}}),{c(){h(e.$$.fragment)},m(t,s){v(e,t,s),n=!0},p(t,s){const o={};1&s&&(o.text=t[0]),2&s&&(o.path=t[1]),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){m(e.$$.fragment,t),n=!1},d(t){w(e,t)}}}function mt(r){let e,n,t,s,o,i;const l=[ft,dt],a=[];function $(c,g){return c[0]!==null&&c[1]!==null?0:1}return n=$(r),t=a[n]=l[n](r),{c(){e=F("div"),t.c(),R(e,"class","c-memories-container svelte-1vchs21")},m(c,g){b(c,e,g),a[n].m(e,null),s=!0,o||(i=Ee(window,"message",r[2].onMessageFromExtension),o=!0)},p(c,[g]){let u=n;n=$(c),n===u?a[n].p(c,g):(D(),m(a[u],1,1,()=>{a[u]=null}),U(),t=a[n],t?t.p(c,g):(t=a[n]=l[n](c),t.c()),f(t,1),t.m(e,null))},i(c){s||(f(t),s=!0)},o(c){m(t),s=!1},d(c){c&&y(e),a[n].d(),o=!1,i()}}}function pt(r,e,n){let t,s;const o=new Y(A),i=H(null);_(r,i,$=>n(0,t=$));const l=H(null);_(r,l,$=>n(1,s=$));const a={handleMessageFromExtension($){const c=$.data;if(c&&c.type===K.loadFile){if(c.data.content!==void 0){const g=c.data.content.replace(/^\n+/,"");i.set(g)}c.data.pathName&&l.set(c.data.pathName)}return!0}};return de(()=>{o.registerConsumer(a),A.postMessage({type:K.memoriesLoaded})}),Te(()=>{o.dispose()}),[t,s,o,i,l]}new class extends P{constructor(r){super(),V(this,r,pt,mt,Q,{})}}({target:document.getElementById("app")});
