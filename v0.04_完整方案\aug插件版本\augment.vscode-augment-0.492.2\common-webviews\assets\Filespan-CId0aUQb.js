import{S as R,i as T,s as V,T as W,I as X,L as Y,u as z,t as _,M as Z,P as j,G as v,Q as x,X as F,c as g,Y as y,e as G,f as m,R as I,V as L,W as M,Z as S,h as A}from"./SpinnerAugment-BEPEN2tu.js";import{n as b,g as k,a as q}from"./file-paths-BcSg4gks.js";const B=e=>({}),P=e=>({}),C=e=>({}),Q=e=>({});function w(e){let a,t,n;return{c(){a=v("div"),t=v("div"),n=F(e[3]),g(t,"class","c-filespan__dir-text svelte-juudge"),g(a,"class","c-filespan__dir svelte-juudge")},m(c,$){G(c,a,$),m(a,t),m(t,n)},p(c,$){8&$&&S(n,c[3])},d(c){c&&A(a)}}}function D(e){let a,t,n,c,$,h,d,o;const f=e[7].leftIcon,l=j(f,e,e[8],Q);let i=!e[2]&&w(e);const p=e[7].rightIcon,u=j(p,e,e[8],P);return{c(){a=v("div"),l&&l.c(),t=x(),n=v("span"),c=F(e[4]),$=x(),i&&i.c(),h=x(),u&&u.c(),g(n,"class","c-filespan__filename svelte-juudge"),g(a,"class",d=y(`c-filespan ${e[0]}`)+" svelte-juudge")},m(s,r){G(s,a,r),l&&l.m(a,null),m(a,t),m(a,n),m(n,c),m(a,$),i&&i.m(a,null),m(a,h),u&&u.m(a,null),o=!0},p(s,r){l&&l.p&&(!o||256&r)&&I(l,f,s,s[8],o?M(f,s[8],r,C):L(s[8]),Q),(!o||16&r)&&S(c,s[4]),s[2]?i&&(i.d(1),i=null):i?i.p(s,r):(i=w(s),i.c(),i.m(a,h)),u&&u.p&&(!o||256&r)&&I(u,p,s,s[8],o?M(p,s[8],r,B):L(s[8]),P),(!o||1&r&&d!==(d=y(`c-filespan ${s[0]}`)+" svelte-juudge"))&&g(a,"class",d)},i(s){o||(z(l,s),z(u,s),o=!0)},o(s){_(l,s),_(u,s),o=!1},d(s){s&&A(a),l&&l.d(s),i&&i.d(),u&&u.d(s)}}}function E(e){let a,t;return a=new W({props:{size:e[1],$$slots:{default:[D]},$$scope:{ctx:e}}}),{c(){X(a.$$.fragment)},m(n,c){Y(a,n,c),t=!0},p(n,[c]){const $={};2&c&&($.size=n[1]),285&c&&($.$$scope={dirty:c,ctx:n}),a.$set($)},i(n){t||(z(a.$$.fragment,n),t=!0)},o(n){_(a.$$.fragment,n),t=!1},d(n){Z(a,n)}}}function H(e,a,t){let n,c,$,{$$slots:h={},$$scope:d}=a,{class:o=""}=a,{filepath:f}=a,{size:l=1}=a,{nopath:i=!1}=a;return e.$$set=p=>{"class"in p&&t(0,o=p.class),"filepath"in p&&t(5,f=p.filepath),"size"in p&&t(1,l=p.size),"nopath"in p&&t(2,i=p.nopath),"$$scope"in p&&t(8,d=p.$$scope)},e.$$.update=()=>{32&e.$$.dirty&&t(6,n=b(f)),64&e.$$.dirty&&t(4,c=k(n)),64&e.$$.dirty&&t(3,$=q(n))},[o,l,i,$,c,f,n,h,d]}class N extends R{constructor(a){super(),T(this,a,H,E,V,{class:0,filepath:5,size:1,nopath:2})}}export{N as F};
