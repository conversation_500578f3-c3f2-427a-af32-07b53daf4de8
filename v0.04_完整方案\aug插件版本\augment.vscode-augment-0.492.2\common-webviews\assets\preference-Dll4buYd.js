import{S as K,i as T,s as X,G as f,Q as b,c,K as L,e as R,f as r,a5 as F,n as j,h as S,a7 as $e,X as se,Z as ie,a4 as ue,ab as ge,I as P,L as z,q as re,t as M,r as ce,u as B,M as E,ao as ve,a3 as be,_ as ee,$ as te,a1 as ne,a9 as ye}from"./SpinnerAugment-BEPEN2tu.js";import{h as H,W as G}from"./BaseButton-DBHsDlhs.js";import{aq as le}from"./AugmentMessage-DtUuYhLr.js";import{C as we,S as ke}from"./folder-DhY9vbAU.js";import{M as xe}from"./TextTooltipAugment-DAVq7Vla.js";import{s as Ce}from"./check-56hMuF8e.js";import{M as qe}from"./index-oyQWDnzB.js";import"./CalloutAugment-DzngRWi1.js";import"./Content-Bm7C6iJ1.js";import"./globals-D0QH3NT1.js";import"./arrow-up-right-from-square-Yo0BPgM2.js";import"./types-DK4HA_lx.js";import"./file-paths-BcSg4gks.js";import"./diff-utils-CHDUdEQq.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-CiMDylqQ.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-BLAu3pyz.js";import"./await_block-DO_bDmS_.js";import"./CollapseButtonAugment-CEo2LYfW.js";import"./IconButtonAugment-DT9AU8SC.js";import"./ButtonAugment-xh-SOBaV.js";import"./github-kew-evZ8.js";import"./index-DY0Q9XhW.js";import"./CardAugment-RCmwRtRa.js";import"./MaterialIcon-BGa1zPPN.js";import"./CopyButton-BVbGAvmE.js";import"./magnifying-glass-BmBw0jRN.js";import"./ellipsis-DsAFryUF.js";import"./IconFilePath-BCQiczSW.js";import"./LanguageIcon-CV8JBzL9.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-CId0aUQb.js";import"./lodash-CujEqTm9.js";import"./mcp-logo-C2xFPiFq.js";import"./terminal-CS_V-nAg.js";import"./pen-to-square-DEUxz3in.js";import"./utils-Rh_q5w_c.js";import"./types-DDm27S8B.js";import"./augment-logo-DTgokSKV.js";import"./types-CGlLNakm.js";import"./file-type-utils-B3gunxPI.js";import"./isObjectLike-C4kUqRHQ.js";import"./TextAreaAugment-C1Wf9cvH.js";import"./ra-diff-ops-model-pDvb0wIq.js";function pe(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","header svelte-1894wv4")},m(e,i){R(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function Ae(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,C,k,w,m,D,q=s[1]&&pe(s);return{c(){t=f("div"),q&&q.c(),n=b(),e=f("div"),i=f("button"),i.textContent="A",o=b(),l=f("button"),l.textContent="A",p=b(),h=f("button"),h.textContent="A",d=b(),u=f("button"),u.textContent="=",A=b(),y=f("button"),y.textContent="B",$=b(),C=f("button"),C.textContent="B",k=b(),w=f("button"),w.textContent="B",c(i,"type","button"),c(i,"class","button large svelte-1894wv4"),L(i,"highlighted",s[0]==="A3"),c(l,"type","button"),c(l,"class","button medium svelte-1894wv4"),L(l,"highlighted",s[0]==="A2"),c(h,"type","button"),c(h,"class","button small svelte-1894wv4"),L(h,"highlighted",s[0]==="A1"),c(u,"type","button"),c(u,"class","button equal svelte-1894wv4"),L(u,"highlighted",s[0]==="="),c(y,"type","button"),c(y,"class","button small svelte-1894wv4"),L(y,"highlighted",s[0]==="B1"),c(C,"type","button"),c(C,"class","button medium svelte-1894wv4"),L(C,"highlighted",s[0]==="B2"),c(w,"type","button"),c(w,"class","button large svelte-1894wv4"),L(w,"highlighted",s[0]==="B3"),c(e,"class","buttons svelte-1894wv4"),c(t,"class","container svelte-1894wv4")},m(g,I){R(g,t,I),q&&q.m(t,null),r(t,n),r(t,e),r(e,i),r(e,o),r(e,l),r(e,p),r(e,h),r(e,d),r(e,u),r(e,A),r(e,y),r(e,$),r(e,C),r(e,k),r(e,w),m||(D=[F(i,"click",s[3]),F(l,"click",s[4]),F(h,"click",s[5]),F(u,"click",s[6]),F(y,"click",s[7]),F(C,"click",s[8]),F(w,"click",s[9])],m=!0)},p(g,[I]){g[1]?q?q.p(g,I):(q=pe(g),q.c(),q.m(t,n)):q&&(q.d(1),q=null),1&I&&L(i,"highlighted",g[0]==="A3"),1&I&&L(l,"highlighted",g[0]==="A2"),1&I&&L(h,"highlighted",g[0]==="A1"),1&I&&L(u,"highlighted",g[0]==="="),1&I&&L(y,"highlighted",g[0]==="B1"),1&I&&L(C,"highlighted",g[0]==="B2"),1&I&&L(w,"highlighted",g[0]==="B3")},i:j,o:j,d(g){g&&S(t),q&&q.d(),m=!1,$e(D)}}}function Be(s,t,n){let{selected:e=null}=t,{question:i=null}=t;function o(l){n(0,e=l)}return s.$$set=l=>{"selected"in l&&n(0,e=l.selected),"question"in l&&n(1,i=l.question)},[e,i,o,()=>o("A3"),()=>o("A2"),()=>o("A1"),()=>o("="),()=>o("B1"),()=>o("B2"),()=>o("B3")]}class ae extends K{constructor(t){super(),T(this,t,Be,Ae,X,{selected:0,question:1})}}function de(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","question svelte-1i0f73l")},m(e,i){R(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function Me(s){let t,n,e,i,o,l=s[1]&&de(s);return{c(){t=f("div"),l&&l.c(),n=b(),e=f("textarea"),c(e,"class","input svelte-1i0f73l"),c(e,"placeholder",s[2]),c(e,"rows","3"),c(t,"class","container svelte-1i0f73l")},m(p,h){R(p,t,h),l&&l.m(t,null),r(t,n),r(t,e),ue(e,s[0]),i||(o=F(e,"input",s[3]),i=!0)},p(p,[h]){p[1]?l?l.p(p,h):(l=de(p),l.c(),l.m(t,n)):l&&(l.d(1),l=null),4&h&&c(e,"placeholder",p[2]),1&h&&ue(e,p[0])},i:j,o:j,d(p){p&&S(t),l&&l.d(),i=!1,o()}}}function De(s,t,n){let{value:e=""}=t,{question:i=null}=t,{placeholder:o=""}=t;return s.$$set=l=>{"value"in l&&n(0,e=l.value),"question"in l&&n(1,i=l.question),"placeholder"in l&&n(2,o=l.placeholder)},[e,i,o,function(){e=this.value,n(0,e)}]}class Ie extends K{constructor(t){super(),T(this,t,De,Me,X,{value:0,question:1,placeholder:2})}}function Re(s){let t,n,e,i;return{c(){t=f("button"),n=se(s[0]),c(t,"class","button svelte-2k5n")},m(o,l){R(o,t,l),r(t,n),e||(i=F(t,"click",function(){ge(s[1])&&s[1].apply(this,arguments)}),e=!0)},p(o,[l]){s=o,1&l&&ie(n,s[0])},i:j,o:j,d(o){o&&S(t),e=!1,i()}}}function Se(s,t,n){let{label:e="Submit"}=t,{onClick:i}=t;return s.$$set=o=>{"label"in o&&n(0,e=o.label),"onClick"in o&&n(1,i=o.onClick)},[e,i]}class We extends K{constructor(t){super(),T(this,t,Se,Re,X,{label:0,onClick:1})}}function me(s){let t,n;return{c(){t=f("div"),n=se(s[1])},m(e,i){R(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function _e(s){let t,n,e,i,o,l,p,h,d=s[1]&&me(s);return{c(){t=f("div"),d&&d.c(),n=b(),e=f("label"),i=f("input"),o=b(),l=f("span"),c(i,"type","checkbox"),c(i,"class","svelte-n0uy88"),c(l,"class","svelte-n0uy88"),c(e,"class","custom-checkbox svelte-n0uy88"),c(t,"class","container svelte-n0uy88")},m(u,A){R(u,t,A),d&&d.m(t,null),r(t,n),r(t,e),r(e,i),i.checked=s[0],r(e,o),r(e,l),p||(h=F(i,"change",s[2]),p=!0)},p(u,[A]){u[1]?d?d.p(u,A):(d=me(u),d.c(),d.m(t,n)):d&&(d.d(1),d=null),1&A&&(i.checked=u[0])},i:j,o:j,d(u){u&&S(t),d&&d.d(),p=!1,h()}}}function Le(s,t,n){let{isChecked:e=!1}=t,{question:i=null}=t;return s.$$set=o=>{"isChecked"in o&&n(0,e=o.isChecked),"question"in o&&n(1,i=o.question)},[e,i,function(){e=this.checked,n(0,e)}]}class Oe extends K{constructor(t){super(),T(this,t,Le,_e,X,{isChecked:0,question:1})}}function Fe(s){let t;return{c(){t=f("p"),t.textContent="Streaming in progress... Please wait for both responses to complete."},m(n,e){R(n,t,e)},p:j,i:j,o:j,d(n){n&&S(t)}}}function Pe(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,C,k,w,m;function D(a){s[12](a)}let q={question:"Which response is formatted better? (e.g. level of detail style, structure)?"};function g(a){s[13](a)}s[2]!==void 0&&(q.selected=s[2]),t=new ae({props:q}),ee.push(()=>te(t,"selected",D));let I={question:"Which response follows your instruction better?"};function Z(a){s[14](a)}s[3]!==void 0&&(I.selected=s[3]),i=new ae({props:I}),ee.push(()=>te(i,"selected",g));let J={question:"Which response is better overall?"};function W(a){s[15](a)}s[1]!==void 0&&(J.selected=s[1]),p=new ae({props:J}),ee.push(()=>te(p,"selected",Z));let _={question:s[9]};function N(a){s[16](a)}s[5]!==void 0&&(_.isChecked=s[5]),u=new Oe({props:_}),ee.push(()=>te(u,"isChecked",W));let U={question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions."};return s[4]!==void 0&&(U.value=s[4]),$=new Ie({props:U}),ee.push(()=>te($,"value",N)),w=new We({props:{label:"Submit",onClick:s[10]}}),{c(){P(t.$$.fragment),e=b(),P(i.$$.fragment),l=b(),P(p.$$.fragment),d=b(),P(u.$$.fragment),y=b(),P($.$$.fragment),k=b(),P(w.$$.fragment)},m(a,x){z(t,a,x),R(a,e,x),z(i,a,x),R(a,l,x),z(p,a,x),R(a,d,x),z(u,a,x),R(a,y,x),z($,a,x),R(a,k,x),z(w,a,x),m=!0},p(a,x){const v={};!n&&4&x&&(n=!0,v.selected=a[2],ne(()=>n=!1)),t.$set(v);const O={};!o&&8&x&&(o=!0,O.selected=a[3],ne(()=>o=!1)),i.$set(O);const V={};!h&&2&x&&(h=!0,V.selected=a[1],ne(()=>h=!1)),p.$set(V);const Q={};512&x&&(Q.question=a[9]),!A&&32&x&&(A=!0,Q.isChecked=a[5],ne(()=>A=!1)),u.$set(Q);const Y={};!C&&16&x&&(C=!0,Y.value=a[4],ne(()=>C=!1)),$.$set(Y)},i(a){m||(B(t.$$.fragment,a),B(i.$$.fragment,a),B(p.$$.fragment,a),B(u.$$.fragment,a),B($.$$.fragment,a),B(w.$$.fragment,a),m=!0)},o(a){M(t.$$.fragment,a),M(i.$$.fragment,a),M(p.$$.fragment,a),M(u.$$.fragment,a),M($.$$.fragment,a),M(w.$$.fragment,a),m=!1},d(a){a&&(S(e),S(l),S(d),S(y),S(k)),E(t,a),E(i,a),E(p,a),E(u,a),E($,a),E(w,a)}}}function ze(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,C,k,w,m,D,q,g,I,Z,J,W,_,N;o=new le({props:{markdown:s[0].data.a.message}}),$=new le({props:{markdown:s[8]}}),g=new le({props:{markdown:s[7]}});const U=[Pe,Fe],a=[];function x(v,O){return v[6]?0:1}return W=x(s),_=a[W]=U[W](s),{c(){t=f("main"),n=f("div"),e=f("h1"),e.textContent="Input message",i=b(),P(o.$$.fragment),l=b(),p=f("hr"),h=b(),d=f("div"),u=f("div"),A=f("h1"),A.textContent="Option A",y=b(),P($.$$.fragment),C=b(),k=f("div"),w=b(),m=f("div"),D=f("h1"),D.textContent="Option B",q=b(),P(g.$$.fragment),I=b(),Z=f("hr"),J=b(),_.c(),c(e,"class","svelte-751nif"),c(p,"class","l-side-by-side svelte-751nif"),c(A,"class","svelte-751nif"),c(u,"class","l-side-by-side__child svelte-751nif"),c(k,"class","divider svelte-751nif"),c(D,"class","svelte-751nif"),c(m,"class","l-side-by-side__child svelte-751nif"),c(d,"class","l-side-by-side svelte-751nif"),c(Z,"class","svelte-751nif"),c(n,"class","l-pref svelte-751nif")},m(v,O){R(v,t,O),r(t,n),r(n,e),r(n,i),z(o,n,null),r(n,l),r(n,p),r(n,h),r(n,d),r(d,u),r(u,A),r(u,y),z($,u,null),r(d,C),r(d,k),r(d,w),r(d,m),r(m,D),r(m,q),z(g,m,null),r(n,I),r(n,Z),r(n,J),a[W].m(n,null),N=!0},p(v,[O]){const V={};1&O&&(V.markdown=v[0].data.a.message),o.$set(V);const Q={};256&O&&(Q.markdown=v[8]),$.$set(Q);const Y={};128&O&&(Y.markdown=v[7]),g.$set(Y);let oe=W;W=x(v),W===oe?a[W].p(v,O):(re(),M(a[oe],1,1,()=>{a[oe]=null}),ce(),_=a[W],_?_.p(v,O):(_=a[W]=U[W](v),_.c()),B(_,1),_.m(n,null))},i(v){N||(B(o.$$.fragment,v),B($.$$.fragment,v),B(g.$$.fragment,v),B(_),N=!0)},o(v){M(o.$$.fragment,v),M($.$$.fragment,v),M(g.$$.fragment,v),M(_),N=!1},d(v){v&&S(t),E(o),E($),E(g),a[W].d()}}}function Ee(s,t,n){let e,i,o,{inputData:l}=t;const p=ve();let h=new we(new xe(H),H,new ke);Ce(h);let d=null,u=null,A=null,y=null,$="",C=!1,k={a:null,b:null},w=l.data.a.response.length>0&&l.data.b.response.length>0;return be(()=>{window.addEventListener("message",m=>{const D=m.data;D.type===G.chatModelReply?(D.stream==="A"?n(11,k.a=D.data.text,k):D.stream==="B"&&n(11,k.b=D.data.text,k),n(11,k)):D.type===G.chatStreamDone&&n(6,w=!0)})}),s.$$set=m=>{"inputData"in m&&n(0,l=m.inputData)},s.$$.update=()=>{var m;2&s.$$.dirty&&n(9,e=(m=y)==="="||m===null?"Is this a high quality comparison?":`Are you completely happy with response '${m.startsWith("A")?"A":"B"}'?`),2049&s.$$.dirty&&n(8,i=k.a!==null?k.a:l.data.a.response),2049&s.$$.dirty&&n(7,o=k.b!==null?k.b:l.data.b.response),1&s.$$.dirty&&n(6,w=l.data.a.response.length>0&&l.data.b.response.length>0)},[l,y,d,u,$,C,w,o,i,e,function(){if(A="=",y===null)return void p("notify","Overall rating is required");p("result",{overallRating:y,formattingRating:d||"=",hallucinationRating:A||"=",instructionFollowingRating:u||"=",isHighQuality:C,textFeedback:$})},k,function(m){d=m,n(2,d)},function(m){u=m,n(3,u)},function(m){y=m,n(1,y)},function(m){C=m,n(5,C)},function(m){$=m,n(4,$)}]}class je extends K{constructor(t){super(),T(this,t,Ee,ze,X,{inputData:0})}}function fe(s){let t,n,e=s[0].type==="Chat"&&he(s);return{c(){e&&e.c(),t=ye()},m(i,o){e&&e.m(i,o),R(i,t,o),n=!0},p(i,o){i[0].type==="Chat"?e?(e.p(i,o),1&o&&B(e,1)):(e=he(i),e.c(),B(e,1),e.m(t.parentNode,t)):e&&(re(),M(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){M(e),n=!1},d(i){i&&S(t),e&&e.d(i)}}}function he(s){let t,n;return t=new je({props:{inputData:s[0]}}),t.$on("result",s[2]),t.$on("notify",s[3]),{c(){P(t.$$.fragment)},m(e,i){z(t,e,i),n=!0},p(e,i){const o={};1&i&&(o.inputData=e[0]),t.$set(o)},i(e){n||(B(t.$$.fragment,e),n=!0)},o(e){M(t.$$.fragment,e),n=!1},d(e){E(t,e)}}}function He(s){let t,n,e=s[0]&&fe(s);return{c(){t=f("main"),e&&e.c()},m(i,o){R(i,t,o),e&&e.m(t,null),n=!0},p(i,o){i[0]?e?(e.p(i,o),1&o&&B(e,1)):(e=fe(i),e.c(),B(e,1),e.m(t,null)):e&&(re(),M(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){M(e),n=!1},d(i){i&&S(t),e&&e.d()}}}function Ne(s){let t,n,e,i;return t=new qe.Root({props:{$$slots:{default:[He]},$$scope:{ctx:s}}}),{c(){P(t.$$.fragment)},m(o,l){z(t,o,l),n=!0,e||(i=F(window,"message",s[1]),e=!0)},p(o,[l]){const p={};17&l&&(p.$$scope={dirty:l,ctx:o}),t.$set(p)},i(o){n||(B(t.$$.fragment,o),n=!0)},o(o){M(t.$$.fragment,o),n=!1},d(o){E(t,o),e=!1,i()}}}function Qe(s,t,n){let e;return H.postMessage({type:G.preferencePanelLoaded}),[e,function(i){const o=i.data;o.type===G.preferenceInit&&n(0,e=o.data)},function(i){const o=i.detail;H.postMessage({type:G.preferenceResultMessage,data:o})},function(i){H.postMessage({type:G.preferenceNotify,data:i.detail})}]}class Ge extends K{constructor(t){super(),T(this,t,Qe,Ne,X,{})}}(async function(){H&&H.initialize&&await H.initialize(),new Ge({target:document.getElementById("app")})})();
