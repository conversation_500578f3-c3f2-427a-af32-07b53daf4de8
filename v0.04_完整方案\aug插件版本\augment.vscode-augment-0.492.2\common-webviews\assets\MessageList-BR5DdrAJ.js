import{S as Me,i as Se,s as Ce,G as P,Q as F,c as H,K as W,e as R,f as z,a6 as X,a5 as re,u as c,q,t as g,r as E,ab as de,h as I,ae as Le,a7 as _e,af as oe,ag as O,a3 as ke,I as L,L as _,M as k,n as b,ah as V,_ as xe,a9 as j}from"./SpinnerAugment-BEPEN2tu.js";import{e as U,u as Re,o as Ie}from"./BaseButton-DBHsDlhs.js";import{t as ve,a as qe,g as Ee,A as De,M as be,R as he,b as Ae,c as Ne,S as Fe,d as He,G as je,P as Be,e as Ge,f as Te,C as We,h as ze,U as Pe,i as we,E as Ue,j as Ke,k as Qe}from"./RemoteAgentRetry-DEfHN_nB.js";import"./Content-Bm7C6iJ1.js";import{S as Y,i as se,a as Je,b as Oe,c as Ve,d as Xe,e as Ye,f as Ze,g as et,h as tt,j as nt,k as rt,E as ot}from"./arrow-up-right-from-square-Yo0BPgM2.js";import"./folder-DhY9vbAU.js";import{R as st}from"./check-56hMuF8e.js";import"./isObjectLike-C4kUqRHQ.js";import{S as at}from"./main-panel-DraraIy5.js";import{aq as lt,ar as it}from"./AugmentMessage-DtUuYhLr.js";import"./types-DDm27S8B.js";import"./MaterialIcon-BGa1zPPN.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./index-CiMDylqQ.js";import"./Keybindings-tA5aIx1O.js";import"./CalloutAugment-DzngRWi1.js";import"./exclamation-triangle-Ovva0uhO.js";import"./CardAugment-RCmwRtRa.js";import"./TextTooltipAugment-DAVq7Vla.js";import"./IconButtonAugment-DT9AU8SC.js";import"./index-DY0Q9XhW.js";import"./pen-to-square-DEUxz3in.js";import"./augment-logo-DTgokSKV.js";import"./ButtonAugment-xh-SOBaV.js";import"./folder-opened-BLAu3pyz.js";import"./expand-QHrAftyc.js";import"./diff-utils-CHDUdEQq.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-oyQWDnzB.js";import"./trash-can-C3k9v09A.js";import"./CollapseButtonAugment-CEo2LYfW.js";import"./github-kew-evZ8.js";import"./index-DqflzTEb.js";import"./utils-Rh_q5w_c.js";import"./globals-D0QH3NT1.js";import"./types-DK4HA_lx.js";import"./file-paths-BcSg4gks.js";import"./types-CGlLNakm.js";import"./file-type-utils-B3gunxPI.js";import"./TextAreaAugment-C1Wf9cvH.js";import"./ra-diff-ops-model-pDvb0wIq.js";import"./design-system-init-BG5cEFim.js";import"./StatusIndicator-By29C4Fu.js";import"./await_block-DO_bDmS_.js";import"./Filespan-CId0aUQb.js";import"./ellipsis-DsAFryUF.js";import"./terminal-CS_V-nAg.js";import"./VSCodeCodicon-DfXdb5mS.js";import"./chat-flags-model-CRX90O9_.js";import"./CopyButton-BVbGAvmE.js";import"./magnifying-glass-BmBw0jRN.js";import"./IconFilePath-BCQiczSW.js";import"./LanguageIcon-CV8JBzL9.js";import"./next-edit-types-904A5ehg.js";import"./lodash-CujEqTm9.js";import"./mcp-logo-C2xFPiFq.js";function ae(o,t,n){const e=o.slice();e[39]=t[n],e[42]=n;const r=e[42]+1===e[12].length;return e[40]=r,e}function le(o,t,n){const e=o.slice();e[43]=t[n].turn,e[44]=t[n].idx;const r=e[44]+1===e[13].length;return e[45]=r,e}function ie(o){let t,n;return t=new De({}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function mt(o){let t,n,e,r;const s=[wt,ht],p=[];function h(i,l){return i[17].enableRichCheckpointInfo?0:1}return t=h(o),n=p[t]=s[t](o),{c(){n.c(),e=j()},m(i,l){p[t].m(i,l),R(i,e,l),r=!0},p(i,l){let a=t;t=h(i),t===a?p[t].p(i,l):(q(),g(p[a],1,1,()=>{p[a]=null}),E(),n=p[t],n?n.p(i,l):(n=p[t]=s[t](i),n.c()),c(n,1),n.m(e.parentNode,e))},i(i){r||(c(n),r=!0)},o(i){g(n),r=!1},d(i){i&&I(e),p[t].d(i)}}}function ut(o){let t,n;return t=new We({props:{group:o[39],chatModel:o[1],turn:o[43],turnIndex:o[44],isLastTurn:o[45],messageListContainer:o[0]}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.group=e[39]),2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.turn=e[43]),4096&r[0]&&(s.turnIndex=e[44]),12288&r[0]&&(s.isLastTurn=e[45]),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ct(o){let t,n;return t=new ze({props:{stage:o[43].stage,iterationId:o[43].iterationId,stageCount:o[43].stageCount}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.stage=e[43].stage),4096&r[0]&&(s.iterationId=e[43].iterationId),4096&r[0]&&(s.stageCount=e[43].stageCount),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function $t(o){let t,n;return t=new Pe({props:{chatModel:o[1],msg:o[43].response_text??""}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.msg=e[43].response_text??""),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function pt(o){let t,n;return t=new lt({props:{group:o[39],markdown:o[43].response_text??"",messageListContainer:o[0]}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.group=e[39]),4096&r[0]&&(s.markdown=e[43].response_text??""),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function gt(o){let t,n;function e(){return o[30](o[43])}return t=new we({props:{turn:o[43],preamble:at,resendTurn:e,$$slots:{default:[yt]},$$scope:{ctx:o}}}),{c(){L(t.$$.fragment)},m(r,s){_(t,r,s),n=!0},p(r,s){o=r;const p={};4096&s[0]&&(p.turn=o[43]),4100&s[0]&&(p.resendTurn=e),69632&s[0]|131072&s[1]&&(p.$$scope={dirty:s,ctx:o}),t.$set(p)},i(r){n||(c(t.$$.fragment,r),n=!0)},o(r){g(t.$$.fragment,r),n=!1},d(r){k(t,r)}}}function ft(o){let t,n;return t=new Ue({props:{flagsModel:o[14],turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};16384&r[0]&&(s.flagsModel=e[14]),4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function dt(o){let t,n;return t=new we({props:{turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ht(o){let t,n;return t=new Ke({props:{turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function wt(o){let t,n;return t=new Qe({props:{turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function yt(o){let t,n;return t=new it({props:{conversationModel:o[16],turn:o[43]}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};65536&r[0]&&(s.conversationModel=e[16]),4096&r[0]&&(s.turn=e[43]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function me(o){let t,n,e,r;function s(){return o[31](o[43])}return{c(){t=P("div"),H(t,"class","c-msg-list__turn-seen")},m(p,h){R(p,t,h),e||(r=X(n=Ne.call(null,t,{onSeen:s,track:o[43].seen_state!==Y.seen})),e=!0)},p(p,h){o=p,n&&de(n.update)&&4096&h[0]&&n.update.call(null,{onSeen:s,track:o[43].seen_state!==Y.seen})},d(p){p&&I(t),e=!1,r()}}}function ue(o,t){let n,e,r,s,p,h,i,l,a,m,u,v,w,M,$=se(t[43]);const S=[dt,ft,gt,pt,$t,ct,ut,mt],f=[];function D(y,C){return 4096&C[0]&&(e=null),4096&C[0]&&(r=null),4096&C[0]&&(s=null),4096&C[0]&&(p=null),4096&C[0]&&(h=null),4096&C[0]&&(i=null),4096&C[0]&&(l=null),4096&C[0]&&(a=null),e==null&&(e=!!Je(y[43])),e?0:(r==null&&(r=!!Oe(y[43])),r?1:(s==null&&(s=!!Ve(y[43])),s?2:(p==null&&(p=!!Xe(y[43])),p?3:(h==null&&(h=!!Ye(y[43])),h?4:(i==null&&(i=!!Ze(y[43])),i?5:(l==null&&(l=!!(et(y[43])||tt(y[43])||nt(y[43]))),l?6:(a==null&&(a=!(!rt(y[43])||y[43].status!==ot.success)),a?7:-1)))))))}~(m=D(t,[-1,-1]))&&(u=f[m]=S[m](t));let x=$&&me(t);return{key:o,first:null,c(){n=j(),u&&u.c(),v=F(),x&&x.c(),w=j(),this.first=n},m(y,C){R(y,n,C),~m&&f[m].m(y,C),R(y,v,C),x&&x.m(y,C),R(y,w,C),M=!0},p(y,C){let A=m;m=D(t=y,C),m===A?~m&&f[m].p(t,C):(u&&(q(),g(f[A],1,1,()=>{f[A]=null}),E()),~m?(u=f[m],u?u.p(t,C):(u=f[m]=S[m](t),u.c()),c(u,1),u.m(v.parentNode,v)):u=null),4096&C[0]&&($=se(t[43])),$?x?x.p(t,C):(x=me(t),x.c(),x.m(w.parentNode,w)):x&&(x.d(1),x=null)},i(y){M||(c(u),M=!0)},o(y){g(u),M=!1},d(y){y&&(I(n),I(v),I(w)),~m&&f[m].d(y),x&&x.d(y)}}}function ce(o){let t,n,e,r,s;const p=[xt,kt,_t,Lt,Ct,St,Mt],h=[];function i(a,m){return a[9]?0:a[6].retryMessage?1:a[6].showResumingRemoteAgent?2:a[6].showPaused?3:a[6].showGeneratingResponse?4:a[6].showAwaitingUserInput?5:a[6].showStopped?6:-1}~(t=i(o))&&(n=h[t]=p[t](o));let l=o[6].showRunningSpacer&&$e();return{c(){n&&n.c(),e=F(),l&&l.c(),r=j()},m(a,m){~t&&h[t].m(a,m),R(a,e,m),l&&l.m(a,m),R(a,r,m),s=!0},p(a,m){let u=t;t=i(a),t===u?~t&&h[t].p(a,m):(n&&(q(),g(h[u],1,1,()=>{h[u]=null}),E()),~t?(n=h[t],n?n.p(a,m):(n=h[t]=p[t](a),n.c()),c(n,1),n.m(e.parentNode,e)):n=null),a[6].showRunningSpacer?l||(l=$e(),l.c(),l.m(r.parentNode,r)):l&&(l.d(1),l=null)},i(a){s||(c(n),s=!0)},o(a){g(n),s=!1},d(a){a&&(I(e),I(r)),~t&&h[t].d(a),l&&l.d(a)}}}function Mt(o){let t,n;return t=new Fe({}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p:b,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function St(o){let t,n;return t=new He({}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p:b,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Ct(o){let t,n;return t=new je({}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p:b,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Lt(o){let t,n;return t=new Be({}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p:b,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function _t(o){let t,n;return t=new Ge({}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p:b,i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function kt(o){let t,n;return t=new Te({props:{message:o[6].retryMessage}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};64&r[0]&&(s.message=e[6].retryMessage),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function xt(o){let t,n;return t=new he({props:{error:o[9].error,onRetry:o[9].onRetry,onDelete:o[9].onDelete}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};512&r[0]&&(s.error=e[9].error),512&r[0]&&(s.onRetry=e[9].onRetry),512&r[0]&&(s.onDelete=e[9].onDelete),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function $e(o){let t;return{c(){t=P("div"),H(t,"class","c-agent-running-spacer svelte-t9khzq")},m(n,e){R(n,t,e)},d(n){n&&I(t)}}}function Rt(o){let t,n,e,r=[],s=new Map,p=U(o[39]);const h=l=>l[43].request_id??`no-request-id-${l[44]}`;for(let l=0;l<p.length;l+=1){let a=le(o,p,l),m=h(a);s.set(m,r[l]=ue(m,a))}let i=o[40]&&ce(o);return{c(){for(let l=0;l<r.length;l+=1)r[l].c();t=F(),i&&i.c(),n=j()},m(l,a){for(let m=0;m<r.length;m+=1)r[m]&&r[m].m(l,a);R(l,t,a),i&&i.m(l,a),R(l,n,a),e=!0},p(l,a){17002503&a[0]&&(p=U(l[39]),q(),r=Re(r,a,h,1,l,p,s,t.parentNode,Ie,ue,t,le),E()),l[40]?i?(i.p(l,a),4096&a[0]&&c(i,1)):(i=ce(l),i.c(),c(i,1),i.m(n.parentNode,n)):i&&(q(),g(i,1,1,()=>{i=null}),E())},i(l){if(!e){for(let a=0;a<p.length;a+=1)c(r[a]);c(i),e=!0}},o(l){for(let a=0;a<r.length;a+=1)g(r[a]);g(i),e=!1},d(l){l&&(I(t),I(n));for(let a=0;a<r.length;a+=1)r[a].d(l);i&&i.d(l)}}}function pe(o){let t,n;return t=new be({props:{class:"c-msg-list__item--grouped",chatModel:o[1],isLastItem:o[40],userControlsScroll:o[3],requestId:o[39][0].turn.request_id,releaseScroll:o[32],messageListContainer:o[0],minHeight:o[40]?o[8]:0,$$slots:{default:[Rt]},$$scope:{ctx:o}}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),4096&r[0]&&(s.isLastItem=e[40]),8&r[0]&&(s.userControlsScroll=e[3]),4096&r[0]&&(s.requestId=e[39][0].turn.request_id),8&r[0]&&(s.releaseScroll=e[32]),1&r[0]&&(s.messageListContainer=e[0]),4352&r[0]&&(s.minHeight=e[40]?e[8]:0),225863&r[0]|131072&r[1]&&(s.$$scope={dirty:r,ctx:e}),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ge(o){let t,n;return t=new he({props:{error:o[9].error,onRetry:o[9].onRetry,onDelete:o[9].onDelete}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};512&r[0]&&(s.error=e[9].error),512&r[0]&&(s.onRetry=e[9].onRetry),512&r[0]&&(s.onDelete=e[9].onDelete),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function fe(o){let t,n;return t=new Ae({props:{messageListElement:o[0],showScrollDown:o[7]}}),{c(){L(t.$$.fragment)},m(e,r){_(t,e,r),n=!0},p(e,r){const s={};1&r[0]&&(s.messageListElement=e[0]),128&r[0]&&(s.showScrollDown=e[7]),t.$set(s)},i(e){n||(c(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function It(o){let t,n,e,r,s,p,h,i,l,a=o[10]&&ie(),m=U(o[12]),u=[];for(let $=0;$<m.length;$+=1)u[$]=pe(ae(o,m,$));const v=$=>g(u[$],1,1,()=>{u[$]=null});let w=!o[13].length&&o[9]&&ge(o),M=o[11]&&fe(o);return{c(){t=P("div"),n=P("div"),a&&a.c(),e=F();for(let $=0;$<u.length;$+=1)u[$].c();r=F(),w&&w.c(),p=F(),M&&M.c(),H(n,"class","c-msg-list svelte-t9khzq"),W(n,"c-msg-list--minimal",!o[17].fullFeatured),H(t,"class","c-msg-list-container svelte-t9khzq"),H(t,"data-testid","chat-message-list"),W(t,"c-msg-list--minimal",!o[17].fullFeatured)},m($,S){R($,t,S),z(t,n),a&&a.m(n,null),z(n,e);for(let f=0;f<u.length;f+=1)u[f]&&u[f].m(n,null);z(n,r),w&&w.m(n,null),o[33](n),z(t,p),M&&M.m(t,null),h=!0,i||(l=[X(ve.call(null,n,{onScrollIntoBottom:o[21],onScrollAwayFromBottom:o[22],onScroll:o[34]})),X(s=qe.call(null,n,{onHeightChange:o[35]})),re(t,"mouseenter",o[36]),re(t,"mouseleave",o[37])],i=!0)},p($,S){if($[10]?a?1024&S[0]&&c(a,1):(a=ie(),a.c(),c(a,1),a.m(n,e)):a&&(q(),g(a,1,1,()=>{a=null}),E()),17003343&S[0]){let f;for(m=U($[12]),f=0;f<m.length;f+=1){const D=ae($,m,f);u[f]?(u[f].p(D,S),c(u[f],1)):(u[f]=pe(D),u[f].c(),c(u[f],1),u[f].m(n,r))}for(q(),f=m.length;f<u.length;f+=1)v(f);E()}!$[13].length&&$[9]?w?(w.p($,S),8704&S[0]&&c(w,1)):(w=ge($),w.c(),c(w,1),w.m(n,null)):w&&(q(),g(w,1,1,()=>{w=null}),E()),s&&de(s.update)&&32&S[0]&&s.update.call(null,{onHeightChange:$[35]}),(!h||131072&S[0])&&W(n,"c-msg-list--minimal",!$[17].fullFeatured),$[11]?M?(M.p($,S),2048&S[0]&&c(M,1)):(M=fe($),M.c(),c(M,1),M.m(t,null)):M&&(q(),g(M,1,1,()=>{M=null}),E()),(!h||131072&S[0])&&W(t,"c-msg-list--minimal",!$[17].fullFeatured)},i($){if(!h){c(a);for(let S=0;S<m.length;S+=1)c(u[S]);c(w),c(M),h=!0}},o($){g(a),u=u.filter(Boolean);for(let S=0;S<u.length;S+=1)g(u[S]);g(w),g(M),h=!1},d($){$&&I(t),a&&a.d(),Le(u,$),w&&w.d(),o[33](null),M&&M.d(),i=!1,_e(l)}}}function vt(o,t,n){let e,r,s,p,h,i,l,a,m,u,v,w,M,$,S,f,D,x=b,y=b,C=()=>(y(),y=V(B,d=>n(29,f=d)),B),A=b;o.$$.on_destroy.push(()=>x()),o.$$.on_destroy.push(()=>y()),o.$$.on_destroy.push(()=>A());let{chatModel:B}=t;C();let{onboardingWorkspaceModel:K}=t,{msgListElement:G}=t;const ye=oe("agentConversationModel"),{agentExchangeStatus:Z,isCurrConversationAgentic:ee}=ye;O(o,Z,d=>n(28,S=d)),O(o,ee,d=>n(27,$=d));const te=oe(st.key);O(o,te,d=>n(26,M=d));let N=!1,T=!1;function Q(){n(3,N=!0)}ke(()=>{var d;((d=w.lastExchange)==null?void 0:d.seen_state)===Y.unseen&&Q()});let J=0;const ne=d=>w.markSeen(d);return o.$$set=d=>{"chatModel"in d&&C(n(1,B=d.chatModel)),"onboardingWorkspaceModel"in d&&n(2,K=d.onboardingWorkspaceModel),"msgListElement"in d&&n(0,G=d.msgListElement)},o.$$.update=()=>{536870912&o.$$.dirty[0]&&(n(15,e=f.currentConversationModel),x(),x=V(e,d=>n(16,w=d))),536870912&o.$$.dirty[0]&&(n(14,r=f.flags),A(),A=V(r,d=>n(17,D=d))),1006632960&o.$$.dirty[0]&&n(25,s=Ee(f,S,$,M)),33554432&o.$$.dirty[0]&&n(13,p=s.chatHistory),33554432&o.$$.dirty[0]&&n(12,h=s.groupedChatHistory),33554432&o.$$.dirty[0]&&n(6,i=s.lastGroupConfig),33554432&o.$$.dirty[0]&&n(11,l=s.doShowFloatingButtons),33554432&o.$$.dirty[0]&&n(10,a=s.doShowAgentSetupLogs),64&o.$$.dirty[0]&&n(9,m=i.remoteAgentErrorConfig),32&o.$$.dirty[0]&&n(8,u=J),24&o.$$.dirty[0]&&n(7,v=N&&T)},[G,B,K,N,T,J,i,v,u,m,a,l,h,p,r,e,w,D,Z,ee,te,function(){n(3,N=!1)},function(){n(3,N=!0)},Q,ne,s,M,$,S,f,d=>K.retryProjectSummary(d),d=>ne(d),()=>n(3,N=!0),function(d){xe[d?"unshift":"push"](()=>{G=d,n(0,G)})},d=>{d<=1&&Q()},d=>n(5,J=d),()=>n(4,T=!0),()=>n(4,T=!1)]}class Nn extends Me{constructor(t){super(),Se(this,t,vt,It,Ce,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{Nn as default};
