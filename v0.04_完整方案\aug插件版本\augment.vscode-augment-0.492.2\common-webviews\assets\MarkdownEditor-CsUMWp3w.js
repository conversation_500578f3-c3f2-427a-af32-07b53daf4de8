import{S as et,i as ot,s as at,P as N,a as A,_ as X,$ as Z,G as K,Q as V,I as D,c as L,e as F,f as b,L as R,R as P,V as Q,W as q,g as ct,a0 as lt,a1 as B,u as f,q as C,t as x,r as H,h as j,M as W,N as J,a2 as rt,j as it,T as st,X as nt,Z as ut}from"./SpinnerAugment-BEPEN2tu.js";import"./BaseButton-DBHsDlhs.js";import"./index-DY0Q9XhW.js";import{T as dt}from"./TextAreaAugment-C1Wf9cvH.js";import{l as $t}from"./lodash-CujEqTm9.js";const mt=o=>({}),O=o=>({}),pt=o=>({}),U=o=>({});function Y(o){let s,e;return s=new st({props:{size:1,weight:"light",color:"error",$$slots:{default:[vt]},$$scope:{ctx:o}}}),{c(){D(s.$$.fragment)},m(a,m){R(s,a,m),e=!0},p(a,m){const p={};4194432&m&&(p.$$scope={dirty:m,ctx:a}),s.$set(p)},i(a){e||(f(s.$$.fragment,a),e=!0)},o(a){x(s.$$.fragment,a),e=!1},d(a){W(s,a)}}}function vt(o){let s;return{c(){s=nt(o[7])},m(e,a){F(e,s,a)},p(e,a){128&a&&ut(s,e[7])},d(e){e&&j(s)}}}function tt(o){let s,e;return s=new st({props:{size:1,weight:"light",color:"success",$$slots:{default:[ft]},$$scope:{ctx:o}}}),{c(){D(s.$$.fragment)},m(a,m){R(s,a,m),e=!0},i(a){e||(f(s.$$.fragment,a),e=!0)},o(a){x(s.$$.fragment,a),e=!1},d(a){W(s,a)}}}function ft(o){let s;return{c(){s=nt("Saved")},m(e,a){F(e,s,a)},d(e){e&&j(s)}}}function xt(o){let s,e,a,m,p,_,u,E,I,S,v,T,l;const g=o[17].header,d=N(g,o,o[22],U),z=o[17].default,r=N(z,o,o[22],null),w=o[17].title,$=N(w,o,o[22],O),h=[{variant:o[2]},{size:o[3]},{color:o[4]},{resize:o[5]},{placeholder:"Enter markdown content..."},o[11]];function k(n){o[18](n)}function G(n){o[19](n)}let y={};for(let n=0;n<h.length;n+=1)y=A(y,h[n]);o[0]!==void 0&&(y.textInput=o[0]),o[1]!==void 0&&(y.value=o[1]),u=new dt({props:y}),X.push(()=>Z(u,"textInput",k)),X.push(()=>Z(u,"value",G)),u.$on("select",o[9]),u.$on("mouseup",o[9]),u.$on("keyup",o[20]),u.$on("input",o[10]),u.$on("keydown",o[21]);let t=!!o[7]&&Y(o),i=o[6]&&tt(o);return{c(){s=K("div"),e=K("div"),d&&d.c(),a=V(),r&&r.c(),m=V(),p=K("div"),$&&$.c(),_=V(),D(u.$$.fragment),S=V(),v=K("div"),t&&t.c(),T=V(),i&&i.c(),L(e,"class","c-markdown-editor__header svelte-1dcrmc3"),L(p,"class","c-markdown-editor__content svelte-1dcrmc3"),L(s,"class","l-markdown-editor svelte-1dcrmc3"),L(v,"class","c-markdown-editor__status svelte-1dcrmc3")},m(n,c){F(n,s,c),b(s,e),d&&d.m(e,null),b(s,a),r&&r.m(s,null),b(s,m),b(s,p),$&&$.m(p,null),b(p,_),R(u,p,null),F(n,S,c),F(n,v,c),t&&t.m(v,null),b(v,T),i&&i.m(v,null),l=!0},p(n,[c]){d&&d.p&&(!l||4194304&c)&&P(d,g,n,n[22],l?q(g,n[22],c,pt):Q(n[22]),U),r&&r.p&&(!l||4194304&c)&&P(r,z,n,n[22],l?q(z,n[22],c,null):Q(n[22]),null),$&&$.p&&(!l||4194304&c)&&P($,w,n,n[22],l?q(w,n[22],c,mt):Q(n[22]),O);const M=2108&c?ct(h,[4&c&&{variant:n[2]},8&c&&{size:n[3]},16&c&&{color:n[4]},32&c&&{resize:n[5]},h[4],2048&c&&lt(n[11])]):{};!E&&1&c&&(E=!0,M.textInput=n[0],B(()=>E=!1)),!I&&2&c&&(I=!0,M.value=n[1],B(()=>I=!1)),u.$set(M),n[7]?t?(t.p(n,c),128&c&&f(t,1)):(t=Y(n),t.c(),f(t,1),t.m(v,T)):t&&(C(),x(t,1,1,()=>{t=null}),H()),n[6]?i?64&c&&f(i,1):(i=tt(n),i.c(),f(i,1),i.m(v,null)):i&&(C(),x(i,1,1,()=>{i=null}),H())},i(n){l||(f(d,n),f(r,n),f($,n),f(u.$$.fragment,n),f(t),f(i),l=!0)},o(n){x(d,n),x(r,n),x($,n),x(u.$$.fragment,n),x(t),x(i),l=!1},d(n){n&&(j(s),j(S),j(v)),d&&d.d(n),r&&r.d(n),$&&$.d(n),W(u),t&&t.d(),i&&i.d()}}}function gt(o,s,e){const a=["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"];let m,p,_=J(s,a),{$$slots:u={},$$scope:E}=s,{variant:I="surface"}=s,{size:S=2}=s,{color:v}=s,{resize:T="none"}=s,{textInput:l}=s,{value:g=""}=s,{selectedText:d=""}=s,{selectionStart:z=0}=s,{selectionEnd:r=0}=s,{saveFunction:w}=s,{debounceValue:$=2500}=s,h=!1;const k=async()=>{try{w(),e(6,h=!0),clearTimeout(m),m=setTimeout(()=>{e(6,h=!1)},1500)}catch(t){e(7,p=t instanceof Error?t.message:String(t))}};function G(){l&&(e(13,z=l.selectionStart),e(14,r=l.selectionEnd),e(12,d=z!==r?g.substring(z,r):""))}const y=$t.debounce(k,$);return rt(()=>{k()}),o.$$set=t=>{s=A(A({},s),it(t)),e(11,_=J(s,a)),"variant"in t&&e(2,I=t.variant),"size"in t&&e(3,S=t.size),"color"in t&&e(4,v=t.color),"resize"in t&&e(5,T=t.resize),"textInput"in t&&e(0,l=t.textInput),"value"in t&&e(1,g=t.value),"selectedText"in t&&e(12,d=t.selectedText),"selectionStart"in t&&e(13,z=t.selectionStart),"selectionEnd"in t&&e(14,r=t.selectionEnd),"saveFunction"in t&&e(15,w=t.saveFunction),"debounceValue"in t&&e(16,$=t.debounceValue),"$$scope"in t&&e(22,E=t.$$scope)},[l,g,I,S,v,T,h,p,k,G,y,_,d,z,r,w,$,u,function(t){l=t,e(0,l)},function(t){g=t,e(1,g)},()=>{G()},t=>{(t.key==="Escape"||(t.metaKey||t.ctrlKey)&&t.key==="s")&&(t.preventDefault(),k())},E]}class It extends et{constructor(s){super(),ot(this,s,gt,xt,at,{variant:2,size:3,color:4,resize:5,textInput:0,value:1,selectedText:12,selectionStart:13,selectionEnd:14,saveFunction:15,debounceValue:16})}}export{It as M};
