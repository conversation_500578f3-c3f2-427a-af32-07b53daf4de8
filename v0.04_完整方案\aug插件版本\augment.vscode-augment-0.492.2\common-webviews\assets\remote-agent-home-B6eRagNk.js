var Kt=Object.defineProperty;var Qt=(i,t,s)=>t in i?Kt(i,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[t]=s;var N=(i,t,s)=>Qt(i,typeof t!="symbol"?t+"":t,s);import{S as J,i as K,s as Q,a as mt,b as Xt,H as Zt,w as Yt,x as te,y as ee,h as p,d as ht,z as se,g as ne,n as tt,j as wt,T as L,G as b,I as y,Q as B,c as x,e as $,L as _,f as O,u as m,t as u,M as S,a2 as Ut,X as T,Z as j,a9 as D,q,r as E,K as Y,af as lt,ag as Gt,a5 as jt,aj as ae,an as vt,a3 as re}from"./SpinnerAugment-BEPEN2tu.js";import"./design-system-init-BG5cEFim.js";import{s as oe}from"./index-DhtTPDph.js";import"./design-system-init-DWM9BlRb.js";import{W as R,R as ie,e as H,u as X,o as Z,h as le}from"./BaseButton-DBHsDlhs.js";import{T as ot,M as ce}from"./TextTooltipAugment-DAVq7Vla.js";import{S as ut,a as de,T as ge,b as me,v as ue,c as pe}from"./StatusIndicator-By29C4Fu.js";import{a as z,R as gt}from"./types-DDm27S8B.js";import{T as it}from"./Content-Bm7C6iJ1.js";import{C as $e}from"./CardAugment-RCmwRtRa.js";import{I as pt}from"./IconButtonAugment-DT9AU8SC.js";import{C as fe}from"./CalloutAugment-DzngRWi1.js";import{T as Vt}from"./terminal-CS_V-nAg.js";import{E as he}from"./exclamation-triangle-Ovva0uhO.js";import{S as we,a as ve}from"./types-CGlLNakm.js";import{A as ye}from"./augment-logo-DTgokSKV.js";import"./utils-Rh_q5w_c.js";import"./index-DqflzTEb.js";import"./globals-D0QH3NT1.js";class _e{constructor(t,s=void 0,e,n){N(this,"subscribers",new Set);this._msgBroker=t,this._state=s,this.validateState=e,this._storeId=n,s&&this.setStateInternal(s)}subscribe(t){return this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}}notifySubscribers(){this.subscribers.forEach(t=>t(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(t,s){return t.id===this.storeId&&this.validateState(s)}update(t){const s=t(this._state);s!==void 0&&this.setStateInternal(s)}setState(t){this.setStateInternal(t)}async setStateInternal(t){JSON.stringify(this._state)!==JSON.stringify(t)&&(this._state=t,this._msgBroker.postMessage({type:R.updateSharedWebviewState,data:t,id:this.storeId}))}async fetchStateFromExtension(){const t=await this._msgBroker.send({type:R.getSharedWebviewState,id:this.storeId,data:{}});t.type===R.getSharedWebviewStateResponse&&this.shouldAcceptMessage(t,t.data)&&(this._state=t.data,this.notifySubscribers())}handleMessageFromExtension(t){switch(t.data.type){case R.updateSharedWebviewState:case R.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(t.data,t.data.data)&&(this._state=t.data.data,this.notifySubscribers(),!0);default:return!1}}}function Se(i){let t,s,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},i[0]],n={};for(let a=0;a<e.length;a+=1)n=mt(n,e[a]);return{c(){t=Xt("svg"),s=new Zt(!0),this.h()},l(a){t=Yt(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=te(t);s=ee(o,!0),o.forEach(p),this.h()},h(){s.a=null,ht(t,n)},m(a,o){se(a,t,o),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',t)},p(a,[o]){ht(t,n=ne(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&o&&a[0]]))},i:tt,o:tt,d(a){a&&p(t)}}}function Re(i,t,s){return i.$$set=e=>{s(0,t=mt(mt({},t),wt(e)))},[t=wt(t)]}class Ae extends J{constructor(t){super(),K(this,t,Re,Se,Q,{})}}class xe extends Error{constructor(t){super(t),this.name="StreamRetryExhaustedError"}}class Jt{constructor(t,s,e,n,a=5,o=4e3,l=2,c){N(this,"_isCancelled",!1);N(this,"_isExhausted",!1);N(this,"streamId");this.agentId=t,this.lastProcessedValue=s,this.startStreamFn=e,this.cancelStreamFn=n,this.maxRetries=a,this.baseDelay=o,this.attemptErrorThreshold=l,this.unhandledErrorMessage=c,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}get isExhausted(){return this._isExhausted}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let t=0;for(;!this._isCancelled;)try{const s=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedValue);for await(const e of s){if(this._isCancelled)return;t=0,yield e}return}catch(s){const e=s instanceof Error?s.message:String(s);if(e===we&&(this._isCancelled=!0),this._isCancelled)return;if(t++,t>this.maxRetries)throw this._isExhausted=!0,new xe(`Failed after ${this.maxRetries} attempts: ${e}`);let n=this.baseDelay*2**(t-1);e===ve?n=0:t>this.attemptErrorThreshold&&(yield{errorMessage:this.unhandledErrorMessage??e,retryAt:new Date(Date.now()+n)}),console.warn(`Retrying remote agent stream in ${n/1e3} seconds... (Attempt ${t} of ${this.maxRetries})`),await new Promise(a=>setTimeout(a,n));continue}}}class ke extends Jt{constructor(t,s,e,n,a=5,o=4e3){super(t,s,e,n,a,o,1,"There was an error connecting to the remote agent.")}}class Ie extends Jt{constructor(t,s,e,n=5,a=4e3){super("overviews",t,s,e,n,a,2,void 0)}}class nt{constructor(t){N(this,"_msgBroker");N(this,"_activeRetryStreams",new Map);N(this,"_activeOverviewsStream");this._msgBroker=t}hasActiveHistoryStream(t){return this._activeRetryStreams.has(t)}getActiveHistoryStream(t){return this._activeRetryStreams.get(t)}get activeHistoryStreams(){return this._activeRetryStreams}hasActiveOverviewsStream(){return this._activeOverviewsStream!==void 0&&!this._activeOverviewsStream.isCancelled}getActiveOverviewsStream(){return this._activeOverviewsStream}async sshToRemoteAgent(t){const s=await this._msgBroker.send({type:R.remoteAgentSshRequest,data:{agentId:t}},1e4);return!!s.data.success||(console.error("Failed to connect to remote agent:",s.data.error),!1)}async deleteRemoteAgent(t,s=!1){return(await this._msgBroker.send({type:R.deleteRemoteAgentRequest,data:{agentId:t,doSkipConfirmation:s}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:R.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:R.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(t){return(await this._msgBroker.send({type:R.getRemoteAgentNotificationEnabledRequest,data:{agentIds:t}})).data}async setRemoteAgentNotificationEnabled(t,s){await this._msgBroker.send({type:R.setRemoteAgentNotificationEnabled,data:{agentId:t,enabled:s}})}async deleteRemoteAgentNotificationEnabled(t){await this._msgBroker.send({type:R.deleteRemoteAgentNotificationEnabled,data:{agentId:t}})}async notifyRemoteAgentReady(t){await this._msgBroker.send({type:R.remoteAgentNotifyReady,data:t})}showRemoteAgentDiffPanel(t){this._msgBroker.postMessage({type:R.showRemoteAgentDiffPanel,data:t})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:R.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(t,s,e=1e4){return await this._msgBroker.send({type:R.getRemoteAgentChatHistoryRequest,data:{agentId:t,lastProcessedSequenceId:s}},e)}async sendRemoteAgentChatRequest(t,s,e=9e4){return this._msgBroker.send({type:R.remoteAgentChatRequest,data:{agentId:t,requestDetails:s,timeoutMs:e}},e)}async interruptRemoteAgent(t,s=1e4){return await this._msgBroker.send({type:R.remoteAgentInterruptRequest,data:{agentId:t}},s)}async createRemoteAgent(t,s,e,n,a,o,l=1e4){return await this._msgBroker.send({type:R.createRemoteAgentRequest,data:{prompt:t,workspaceSetup:s,setupScript:e,isSetupScriptAgent:n,modelId:a,remoteAgentCreationMetrics:o}},l)}async getRemoteAgentOverviews(t=1e4){return await this._msgBroker.send({type:R.getRemoteAgentOverviewsRequest},t)}async listSetupScripts(t=5e3){return await this._msgBroker.send({type:R.listSetupScriptsRequest},t)}async saveSetupScript(t,s,e,n=5e3){return await this._msgBroker.send({type:R.saveSetupScriptRequest,data:{name:t,content:s,location:e}},n)}async deleteSetupScript(t,s,e=5e3){return await this._msgBroker.send({type:R.deleteSetupScriptRequest,data:{name:t,location:s}},e)}async renameSetupScript(t,s,e,n=5e3){return await this._msgBroker.send({type:R.renameSetupScriptRequest,data:{oldName:t,newName:s,location:e}},n)}async getRemoteAgentWorkspaceLogs(t,s,e,n=1e4){return await this._msgBroker.send({type:R.remoteAgentWorkspaceLogsRequest,data:{agentId:t,lastProcessedStep:s,lastProcessedSequenceId:e}},n)}async saveLastRemoteAgentSetup(t,s,e){return await this._msgBroker.send({type:R.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:t,lastRemoteAgentGitBranch:s,lastRemoteAgentSetupScript:e}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:R.getLastRemoteAgentSetupRequest})}async*startRemoteAgentOverviewsStream(t,s,e=6e4,n=3e5){const a={type:R.remoteAgentOverviewsStreamRequest,data:{streamId:t,lastUpdateTimestamp:s}},o=this._msgBroker.stream(a,e,n);for await(const l of o){if(l.data.error)throw new Error(l.data.error);yield l.data.response}}async*startRemoteAgentHistoryStream(t,s,e,n=6e4,a=3e5){const o={type:R.remoteAgentHistoryStreamRequest,data:{streamId:s,agentId:t,lastProcessedSequenceId:e}},l=this._msgBroker.stream(o,n,a);for await(const c of l)yield c.data}async*startRemoteAgentsListStreamWithRetry(t,s=5,e=4e3){var a;const n=new Ie(t,(o,l,c)=>this.startRemoteAgentOverviewsStream(l,c),o=>this._closeRemoteAgentsStream(o),s,e);(a=this._activeOverviewsStream)==null||a.cancel(),this._activeOverviewsStream=n;try{yield*n.getStream()}finally{n.isCancelled||n.isExhausted||(this._activeOverviewsStream=void 0)}}async*startRemoteAgentHistoryStreamWithRetry(t,s,e=5,n=4e3){var o;const a=new ke(t,s,(l,c,r)=>this.startRemoteAgentHistoryStream(l,c,r),l=>this._closeRemoteAgentsStream(l),e,n);(o=this._activeRetryStreams.get(t))==null||o.cancel(),this._activeRetryStreams.set(t,a);try{yield*a.getStream()}finally{a.isCancelled||this._activeRetryStreams.delete(t)}}cancelRemoteAgentOverviewsStream(){this._activeOverviewsStream&&(this._activeOverviewsStream.cancel(),this._activeOverviewsStream=void 0)}cancelRemoteAgentHistoryStream(t){const s=this._activeRetryStreams.get(t);s&&(s.cancel(),this._activeRetryStreams.delete(t))}async _closeRemoteAgentsStream(t){await this._msgBroker.send({type:R.cancelRemoteAgentsStreamRequest,data:{streamId:t}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(t=>{t.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelRemoteAgentOverviewsStream(),this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:R.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(t){return console.error("Failed to get pinned agents from store:",t),{}}}async savePinnedAgentToStore(t,s){try{await this._msgBroker.send({type:R.setRemoteAgentPinnedStatus,data:{agentId:t,isPinned:s}})}catch(e){console.error("Failed to save pinned agent to store:",e)}}async deletePinnedAgentFromStore(t){try{await this._msgBroker.send({type:R.deleteRemoteAgentPinnedStatus,data:{agentId:t}})}catch(s){console.error("Failed to delete pinned agent from store:",s)}}async openDiffInBuffer(t,s,e){return await this._msgBroker.send({type:R.openDiffInBuffer,data:{oldContents:t,newContents:s,filePath:e}})}async pauseRemoteAgentWorkspace(t){return await this._msgBroker.send({type:R.remoteAgentPauseRequest,data:{agentId:t}},3e4)}async resumeRemoteAgentWorkspace(t){return await this._msgBroker.send({type:R.remoteAgentResumeRequest,data:{agentId:t}},9e4)}async resumeHintRemoteAgent(t,s=ie.viewingAgent){return await this._msgBroker.send({type:R.remoteAgentResumeHintRequest,data:{agentId:t,hintReason:s}},1e4)}async updateRemoteAgentTitle(t,s,e=1e4){return await this._msgBroker.send({type:R.updateRemoteAgentRequest,data:{agentId:t,newTitle:s}},e)}async reportRemoteAgentEvent(t){await this._msgBroker.send({type:R.reportRemoteAgentEvent,data:t})}async getRemoteAgentStatus(){return await this._msgBroker.send({type:R.getRemoteAgentStatus})}async focusAugmentPanel(){await this._msgBroker.send({type:R.showAugmentPanel})}}N(nt,"key","remoteAgentsClient");function yt(i){return function(t){try{if(isNaN(t.getTime()))return"Unknown time";const s=new Date().getTime()-t.getTime(),e=Math.floor(s/1e3),n=Math.floor(e/60),a=Math.floor(n/60),o=Math.floor(a/24);return e<60?`${e}s ago`:n<60?`${n}m ago`:a<24?`${a}h ago`:o<30?`${o}d ago`:t.toLocaleDateString()}catch(s){return console.error("Error formatting date:",s),"Unknown time"}}(new Date(i))}function be(i){let t,s=i[0]?"Running in the cloud":"Running locally";return{c(){t=T(s)},m(e,n){$(e,t,n)},p(e,n){1&n&&s!==(s=e[0]?"Running in the cloud":"Running locally")&&j(t,s)},d(e){e&&p(t)}}}function Be(i){let t;return{c(){t=T("Unknown time")},m(s,e){$(s,t,e)},p:tt,d(s){s&&p(t)}}}function Pe(i){let t;return{c(){t=T(i[3])},m(s,e){$(s,t,e)},p(s,e){8&e&&j(t,s[3])},d(s){s&&p(t)}}}function qe(i){let t,s,e,n=i[1]===z.agentRunning?"Last updated":"Started";function a(c,r){return c[2]?Pe:Be}let o=a(i),l=o(i);return{c(){t=T(n),s=B(),l.c(),e=D()},m(c,r){$(c,t,r),$(c,s,r),l.m(c,r),$(c,e,r)},p(c,r){2&r&&n!==(n=c[1]===z.agentRunning?"Last updated":"Started")&&j(t,n),o===(o=a(c))&&l?l.p(c,r):(l.d(1),l=o(c),l&&(l.c(),l.m(e.parentNode,e)))},d(c){c&&(p(t),p(s),p(e)),l.d(c)}}}function Ee(i){let t,s,e,n,a,o;return s=new L({props:{size:1,color:"secondary",class:"location-text",$$slots:{default:[be]},$$scope:{ctx:i}}}),a=new L({props:{size:1,color:"secondary",class:"time-text",$$slots:{default:[qe]},$$scope:{ctx:i}}}),{c(){t=b("div"),y(s.$$.fragment),e=B(),n=b("div"),y(a.$$.fragment),x(n,"class","time-container"),x(t,"class","agent-card-footer svelte-1qwlkoj")},m(l,c){$(l,t,c),_(s,t,null),O(t,e),O(t,n),_(a,n,null),o=!0},p(l,[c]){const r={};33&c&&(r.$$scope={dirty:c,ctx:l}),s.$set(r);const d={};46&c&&(d.$$scope={dirty:c,ctx:l}),a.$set(d)},i(l){o||(m(s.$$.fragment,l),m(a.$$.fragment,l),o=!0)},o(l){u(s.$$.fragment,l),u(a.$$.fragment,l),o=!1},d(l){l&&p(t),S(s),S(a)}}}function Oe(i,t,s){let{isRemote:e=!1}=t,{status:n}=t,{timestamp:a}=t,o=yt(a);const l=function(c,r){let d=1e3;const g=new Date(c),w=setInterval(()=>{const h=Math.floor((new Date().getTime()-g.getTime())/1e3/60);h>=1&&(d=6e4),h>=60&&(d=36e5),h>=1440&&(d=864e5),r(yt(c))},d);return()=>clearInterval(w)}(a,c=>{s(3,o=c)});return Ut(()=>{l()}),i.$$set=c=>{"isRemote"in c&&s(0,e=c.isRemote),"status"in c&&s(1,n=c.status),"timestamp"in c&&s(2,a=c.timestamp)},[e,n,a,o]}class Me extends J{constructor(t){super(),K(this,t,Oe,Ee,Q,{isRemote:0,status:1,timestamp:2})}}function He(i){let t;return{c(){t=T(i[0])},m(s,e){$(s,t,e)},p(s,e){1&e&&j(t,s[0])},d(s){s&&p(t)}}}function Te(i){let t,s,e;return s=new L({props:{size:1,color:"secondary",$$slots:{default:[He]},$$scope:{ctx:i}}}),{c(){t=b("div"),y(s.$$.fragment),x(t,"class","task-text-container svelte-1tatwxk")},m(n,a){$(n,t,a),_(s,t,null),e=!0},p(n,a){const o={};9&a&&(o.$$scope={dirty:a,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),S(s)}}}function _t(i){let t,s,e;return s=new L({props:{size:1,color:i[1]==="error"?"error":"neutral",$$slots:{default:[Ce]},$$scope:{ctx:i}}}),{c(){t=b("div"),y(s.$$.fragment),x(t,"class","task-status-indicator svelte-1tatwxk")},m(n,a){$(n,t,a),_(s,t,null),e=!0},p(n,a){const o={};2&a&&(o.color=n[1]==="error"?"error":"neutral"),10&a&&(o.$$scope={dirty:a,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),S(s)}}}function Ce(i){let t,s=i[1]==="error"?"!":i[1]==="warning"?"⚠":"";return{c(){t=T(s)},m(e,n){$(e,t,n)},p(e,n){2&n&&s!==(s=e[1]==="error"?"!":e[1]==="warning"?"⚠":"")&&j(t,s)},d(e){e&&p(t)}}}function Fe(i){let t,s,e,n,a,o,l;a=new ot({props:{content:i[0],triggerOn:[it.Hover],maxWidth:"400px",$$slots:{default:[Te]},$$scope:{ctx:i}}});let c=(i[1]==="error"||i[1]==="warning")&&_t(i);return{c(){t=b("div"),s=b("div"),n=B(),y(a.$$.fragment),o=B(),c&&c.c(),x(s,"class",e="bullet-point "+i[2]+" svelte-1tatwxk"),x(t,"class","task-item svelte-1tatwxk")},m(r,d){$(r,t,d),O(t,s),O(t,n),_(a,t,null),O(t,o),c&&c.m(t,null),l=!0},p(r,[d]){(!l||4&d&&e!==(e="bullet-point "+r[2]+" svelte-1tatwxk"))&&x(s,"class",e);const g={};1&d&&(g.content=r[0]),9&d&&(g.$$scope={dirty:d,ctx:r}),a.$set(g),r[1]==="error"||r[1]==="warning"?c?(c.p(r,d),2&d&&m(c,1)):(c=_t(r),c.c(),m(c,1),c.m(t,null)):c&&(q(),u(c,1,1,()=>{c=null}),E())},i(r){l||(m(a.$$.fragment,r),m(c),l=!0)},o(r){u(a.$$.fragment,r),u(c),l=!1},d(r){r&&p(t),S(a),c&&c.d()}}}function De(i,t,s){let e,{text:n}=t,{status:a="info"}=t;return i.$$set=o=>{"text"in o&&s(0,n=o.text),"status"in o&&s(1,a=o.status)},i.$$.update=()=>{2&i.$$.dirty&&s(2,e=function(o){switch(o){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(a))},[n,a,e]}class Ne extends J{constructor(t){super(),K(this,t,De,Fe,Q,{text:0,status:1})}}function St(i,t,s){const e=i.slice();return e[24]=t[s],e[26]=s,e}function Rt(i){let t,s,e;return s=new fe({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Le],default:[ze]},$$scope:{ctx:i}}}),{c(){t=b("div"),y(s.$$.fragment),x(t,"class","deletion-error svelte-1bxdvw4")},m(n,a){$(n,t,a),_(s,t,null),e=!0},p(n,a){const o={};134217744&a&&(o.$$scope={dirty:a,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),S(s)}}}function ze(i){let t,s,e,n,a;return{c(){t=T(i[4]),s=B(),e=b("button"),e.textContent="×",x(e,"class","error-dismiss svelte-1bxdvw4"),x(e,"aria-label","Dismiss error")},m(o,l){$(o,t,l),$(o,s,l),$(o,e,l),n||(a=jt(e,"click",i[11]),n=!0)},p(o,l){16&l&&j(t,o[4])},d(o){o&&(p(t),p(s),p(e)),n=!1,a()}}}function Le(i){let t,s;return t=new he({props:{slot:"icon"}}),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},p:tt,i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function We(i){let t,s;return t=new L({props:{size:2,weight:"medium",class:"session-text",$$slots:{default:[Ge]},$$scope:{ctx:i}}}),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},p(e,n){const a={};134217729&n&&(a.$$scope={dirty:n,ctx:e}),t.$set(a)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function Ue(i){let t,s,e,n,a,o;return e=new Vt({}),a=new L({props:{size:2,weight:"medium",$$slots:{default:[je]},$$scope:{ctx:i}}}),{c(){t=b("div"),s=b("div"),y(e.$$.fragment),n=B(),y(a.$$.fragment),x(s,"class","setup-script-badge svelte-1bxdvw4"),x(t,"class","setup-script-title-container svelte-1bxdvw4")},m(l,c){$(l,t,c),O(t,s),_(e,s,null),O(t,n),_(a,t,null),o=!0},p(l,c){const r={};134217728&c&&(r.$$scope={dirty:c,ctx:l}),a.$set(r)},i(l){o||(m(e.$$.fragment,l),m(a.$$.fragment,l),o=!0)},o(l){u(e.$$.fragment,l),u(a.$$.fragment,l),o=!1},d(l){l&&p(t),S(e),S(a)}}}function Ge(i){let t,s=i[0].session_summary+"";return{c(){t=T(s)},m(e,n){$(e,t,n)},p(e,n){1&n&&s!==(s=e[0].session_summary+"")&&j(t,s)},d(e){e&&p(t)}}}function je(i){let t;return{c(){t=b("span"),t.textContent="Generate a setup script",x(t,"class","setup-script-title svelte-1bxdvw4")},m(s,e){$(s,t,e)},p:tt,d(s){s&&p(t)}}}function At(i){let t,s,e=[],n=new Map,a=H(i[8].slice(0,3));const o=l=>l[26];for(let l=0;l<a.length;l+=1){let c=St(i,a,l),r=o(c);n.set(r,e[l]=xt(r,c))}return{c(){t=b("div");for(let l=0;l<e.length;l+=1)e[l].c();x(t,"class","tasks-list svelte-1bxdvw4")},m(l,c){$(l,t,c);for(let r=0;r<e.length;r+=1)e[r]&&e[r].m(t,null);s=!0},p(l,c){256&c&&(a=H(l[8].slice(0,3)),q(),e=X(e,c,o,1,l,a,n,t,Z,xt,null,St),E())},i(l){if(!s){for(let c=0;c<a.length;c+=1)m(e[c]);s=!0}},o(l){for(let c=0;c<e.length;c+=1)u(e[c]);s=!1},d(l){l&&p(t);for(let c=0;c<e.length;c+=1)e[c].d()}}}function xt(i,t){let s,e,n;return e=new Ne({props:{text:t[24],status:"success"}}),{key:i,first:null,c(){s=D(),y(e.$$.fragment),this.first=s},m(a,o){$(a,s,o),_(e,a,o),n=!0},p(a,o){t=a;const l={};256&o&&(l.text=t[24]),e.$set(l)},i(a){n||(m(e.$$.fragment,a),n=!0)},o(a){u(e.$$.fragment,a),n=!1},d(a){a&&p(s),S(e,a)}}}function Ve(i){let t,s;return t=new me({}),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function Je(i){let t,s;return t=new Ae({}),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function Ke(i){let t,s,e,n;const a=[Je,Ve],o=[];function l(c,r){return c[7]?0:1}return t=l(i),s=o[t]=a[t](i),{c(){s.c(),e=D()},m(c,r){o[t].m(c,r),$(c,e,r),n=!0},p(c,r){let d=t;t=l(c),t!==d&&(q(),u(o[d],1,1,()=>{o[d]=null}),E(),s=o[t],s||(s=o[t]=a[t](c),s.c()),m(s,1),s.m(e.parentNode,e))},i(c){n||(m(s),n=!0)},o(c){u(s),n=!1},d(c){c&&p(e),o[t].d(c)}}}function Qe(i){let t,s;return t=new pt({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ke]},$$scope:{ctx:i}}}),t.$on("click",i[16]),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},p(e,n){const a={};134217856&n&&(a.$$scope={dirty:n,ctx:e}),t.$set(a)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function Xe(i){let t,s;return t=new Vt({}),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function Ze(i){let t,s;return t=new pt({props:{disabled:!i[5],variant:"ghost",color:"neutral",size:1,title:i[5]?"SSH to agent":"SSH to agent (agent must be running or idle)",$$slots:{default:[Xe]},$$scope:{ctx:i}}}),t.$on("click",i[17]),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},p(e,n){const a={};32&n&&(a.disabled=!e[5]),32&n&&(a.title=e[5]?"SSH to agent":"SSH to agent (agent must be running or idle)"),134217728&n&&(a.$$scope={dirty:n,ctx:e}),t.$set(a)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function Ye(i){let t,s;return t=new ge({}),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function ts(i){let t,s;return t=new pt({props:{variant:"ghost",color:"neutral",size:1,disabled:i[3],title:i[3]?"Deleting agent...":"Delete agent",$$slots:{default:[Ye]},$$scope:{ctx:i}}}),t.$on("click",i[18]),{c(){y(t.$$.fragment)},m(e,n){_(t,e,n),s=!0},p(e,n){const a={};8&n&&(a.disabled=e[3]),8&n&&(a.title=e[3]?"Deleting agent...":"Delete agent"),134217728&n&&(a.$$scope={dirty:n,ctx:e}),t.$set(a)},i(e){s||(m(t.$$.fragment,e),s=!0)},o(e){u(t.$$.fragment,e),s=!1},d(e){S(t,e)}}}function es(i){let t,s,e,n,a,o,l,c,r,d,g,w,h,v,A,V,C,W,F,U;const I=[Ue,We],M=[];function G(f,k){return f[0].is_setup_script_agent?0:1}e=G(i),n=M[e]=I[e](i),c=new de({props:{status:i[0].status,workspaceStatus:i[0].workspace_status,isExpanded:!0,hasUpdates:i[0].has_updates}});let P=i[8].length>0&&At(i);return h=new ot({props:{content:i[7]?"Unpin agent":"Pin agent",triggerOn:[it.Hover],side:"top",$$slots:{default:[Qe]},$$scope:{ctx:i}}}),A=new ot({props:{content:"SSH to agent",triggerOn:[it.Hover],side:"top",$$slots:{default:[Ze]},$$scope:{ctx:i}}}),C=new ot({props:{content:"Delete agent",triggerOn:[it.Hover],side:"top",$$slots:{default:[ts]},$$scope:{ctx:i}}}),F=new Me({props:{isRemote:i[6],status:i[0].status,timestamp:i[0].updated_at||i[0].started_at}}),{c(){t=b("div"),s=b("div"),n.c(),o=B(),l=b("div"),y(c.$$.fragment),r=B(),d=b("div"),P&&P.c(),g=B(),w=b("div"),y(h.$$.fragment),v=B(),y(A.$$.fragment),V=B(),y(C.$$.fragment),W=B(),y(F.$$.fragment),x(s,"class","session-summary-container svelte-1bxdvw4"),x(s,"title",a=i[0].is_setup_script_agent?"Generate a setup script":i[0].session_summary),x(l,"class","card-info"),x(t,"class","card-header svelte-1bxdvw4"),x(d,"class","card-content svelte-1bxdvw4"),x(w,"class","card-actions svelte-1bxdvw4")},m(f,k){$(f,t,k),O(t,s),M[e].m(s,null),O(t,o),O(t,l),_(c,l,null),$(f,r,k),$(f,d,k),P&&P.m(d,null),$(f,g,k),$(f,w,k),_(h,w,null),O(w,v),_(A,w,null),O(w,V),_(C,w,null),$(f,W,k),_(F,f,k),U=!0},p(f,k){let ct=e;e=G(f),e===ct?M[e].p(f,k):(q(),u(M[ct],1,1,()=>{M[ct]=null}),E(),n=M[e],n?n.p(f,k):(n=M[e]=I[e](f),n.c()),m(n,1),n.m(s,null)),(!U||1&k&&a!==(a=f[0].is_setup_script_agent?"Generate a setup script":f[0].session_summary))&&x(s,"title",a);const at={};1&k&&(at.status=f[0].status),1&k&&(at.workspaceStatus=f[0].workspace_status),1&k&&(at.hasUpdates=f[0].has_updates),c.$set(at),f[8].length>0?P?(P.p(f,k),256&k&&m(P,1)):(P=At(f),P.c(),m(P,1),P.m(d,null)):P&&(q(),u(P,1,1,()=>{P=null}),E());const dt={};128&k&&(dt.content=f[7]?"Unpin agent":"Pin agent"),134217857&k&&(dt.$$scope={dirty:k,ctx:f}),h.$set(dt);const $t={};134217760&k&&($t.$$scope={dirty:k,ctx:f}),A.$set($t);const ft={};134217737&k&&(ft.$$scope={dirty:k,ctx:f}),C.$set(ft);const rt={};64&k&&(rt.isRemote=f[6]),1&k&&(rt.status=f[0].status),1&k&&(rt.timestamp=f[0].updated_at||f[0].started_at),F.$set(rt)},i(f){U||(m(n),m(c.$$.fragment,f),m(P),m(h.$$.fragment,f),m(A.$$.fragment,f),m(C.$$.fragment,f),m(F.$$.fragment,f),U=!0)},o(f){u(n),u(c.$$.fragment,f),u(P),u(h.$$.fragment,f),u(A.$$.fragment,f),u(C.$$.fragment,f),u(F.$$.fragment,f),U=!1},d(f){f&&(p(t),p(r),p(d),p(g),p(w),p(W)),M[e].d(),S(c),P&&P.d(),S(h),S(A),S(C),S(F,f)}}}function ss(i){let t,s,e,n,a=i[4]&&Rt(i);return e=new $e({props:{variant:"surface",size:2,interactive:!0,class:"agent-card",$$slots:{default:[es]},$$scope:{ctx:i}}}),e.$on("click",i[19]),e.$on("keydown",i[20]),{c(){t=b("div"),a&&a.c(),s=B(),y(e.$$.fragment),x(t,"class","card-wrapper svelte-1bxdvw4"),Y(t,"selected-card",i[1]),Y(t,"setup-script-card",i[0].is_setup_script_agent),Y(t,"deleting",i[3])},m(o,l){$(o,t,l),a&&a.m(t,null),O(t,s),_(e,t,null),n=!0},p(o,[l]){o[4]?a?(a.p(o,l),16&l&&m(a,1)):(a=Rt(o),a.c(),m(a,1),a.m(t,s)):a&&(q(),u(a,1,1,()=>{a=null}),E());const c={};134218217&l&&(c.$$scope={dirty:l,ctx:o}),e.$set(c),(!n||2&l)&&Y(t,"selected-card",o[1]),(!n||1&l)&&Y(t,"setup-script-card",o[0].is_setup_script_agent),(!n||8&l)&&Y(t,"deleting",o[3])},i(o){n||(m(a),m(e.$$.fragment,o),n=!0)},o(o){u(a),u(e.$$.fragment,o),n=!1},d(o){o&&p(t),a&&a.d(),S(e)}}}function ns(i,t,s){let e,n,a,o,l,c,{agent:r}=t,{selected:d=!1}=t,{onSelect:g}=t;const w=lt(nt.key),h=lt(ut);Gt(i,h,I=>s(15,c=I));let v=!1,A=null,V=null;async function C(I){var G,P;W(),s(3,v=!0);const M=((G=c.state)==null?void 0:G.agentOverviews)||[];try{if(!await w.deleteRemoteAgent(I))throw new Error("Failed to delete agent");if(h.update(f=>{if(f)return{...f,agentOverviews:f.agentOverviews.filter(k=>k.remote_agent_id!==I)}}),(((P=c.state)==null?void 0:P.pinnedAgents)||{})[I])try{await w.deletePinnedAgentFromStore(I);const f=await w.getPinnedAgentsFromStore();h.update(k=>{if(k)return{...k,pinnedAgents:f}})}catch(f){console.error("Failed to remove pinned status:",f)}}catch(f){console.error("Failed to delete agent:",f),h.update(k=>{if(k)return{...k,agentOverviews:M}}),s(4,A=f instanceof Error?f.message:"Failed to delete agent"),V=setTimeout(()=>{W()},5e3)}finally{s(3,v=!1)}}function W(){s(4,A=null),V&&(clearTimeout(V),V=null)}async function F(I){try{a?await w.deletePinnedAgentFromStore(I):await w.savePinnedAgentToStore(I,!0);const M=await w.getPinnedAgentsFromStore();h.update(G=>{if(G)return{...G,pinnedAgents:M}})}catch(M){console.error("Failed to toggle pinned status:",M)}}function U(){o&&(async I=>{await w.sshToRemoteAgent(I.remote_agent_id)})(r)}return Ut(()=>{W()}),i.$$set=I=>{"agent"in I&&s(0,r=I.agent),"selected"in I&&s(1,d=I.selected),"onSelect"in I&&s(2,g=I.onSelect)},i.$$.update=()=>{var I;1&i.$$.dirty&&s(8,e=r.turn_summaries||[]),32768&i.$$.dirty&&s(14,n=((I=c.state)==null?void 0:I.pinnedAgents)||{}),16385&i.$$.dirty&&s(7,a=(n==null?void 0:n[r.remote_agent_id])===!0),1&i.$$.dirty&&s(5,o=r.status===z.agentRunning||r.status===z.agentIdle)},s(6,l=!0),[r,d,g,v,A,o,!0,a,e,h,C,W,F,U,n,c,I=>{I.stopPropagation(),F(r.remote_agent_id)},I=>{I.stopPropagation(),U()},I=>{I.stopPropagation(),C(r.remote_agent_id)},()=>g(r.remote_agent_id),I=>I.key==="Enter"&&g(r.remote_agent_id)]}class et extends J{constructor(t){super(),K(this,t,ns,ss,Q,{agent:0,selected:1,onSelect:2})}}function as(i){let t;return{c(){t=T(i[0])},m(s,e){$(s,t,e)},p(s,e){1&e&&j(t,s[0])},d(s){s&&p(t)}}}function rs(i){let t,s,e;return s=new L({props:{size:2,color:"secondary",$$slots:{default:[as]},$$scope:{ctx:i}}}),{c(){t=b("div"),y(s.$$.fragment),x(t,"class","section-header svelte-1tegnqi")},m(n,a){$(n,t,a),_(s,t,null),e=!0},p(n,[a]){const o={};3&a&&(o.$$scope={dirty:a,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),S(s)}}}function os(i,t,s){let{title:e}=t;return i.$$set=n=>{"title"in n&&s(0,e=n.title)},[e]}class st extends J{constructor(t){super(),K(this,t,os,rs,Q,{title:0})}}function kt(i,t,s){const e=i.slice();return e[7]=t[s],e[9]=s,e}function It(i,t,s){const e=i.slice();return e[7]=t[s],e[9]=s,e}function bt(i,t,s){const e=i.slice();return e[7]=t[s],e[9]=s,e}function Bt(i,t,s){const e=i.slice();return e[7]=t[s],e[9]=s,e}function Pt(i,t,s){const e=i.slice();return e[7]=t[s],e[9]=s,e}function qt(i,t,s){const e=i.slice();return e[7]=t[s],e[9]=s,e}function is(i){let t,s,e,n,a,o,l,c=i[1].pinned.length>0&&Et(i),r=i[1].readyToReview.length>0&&Mt(i),d=i[1].running.length>0&&Tt(i),g=i[1].idle.length>0&&Ft(i),w=i[1].failed.length>0&&Nt(i),h=i[1].additional.length>0&&Lt(i);return{c(){c&&c.c(),t=B(),r&&r.c(),s=B(),d&&d.c(),e=B(),g&&g.c(),n=B(),w&&w.c(),a=B(),h&&h.c(),o=D()},m(v,A){c&&c.m(v,A),$(v,t,A),r&&r.m(v,A),$(v,s,A),d&&d.m(v,A),$(v,e,A),g&&g.m(v,A),$(v,n,A),w&&w.m(v,A),$(v,a,A),h&&h.m(v,A),$(v,o,A),l=!0},p(v,A){v[1].pinned.length>0?c?(c.p(v,A),2&A&&m(c,1)):(c=Et(v),c.c(),m(c,1),c.m(t.parentNode,t)):c&&(q(),u(c,1,1,()=>{c=null}),E()),v[1].readyToReview.length>0?r?(r.p(v,A),2&A&&m(r,1)):(r=Mt(v),r.c(),m(r,1),r.m(s.parentNode,s)):r&&(q(),u(r,1,1,()=>{r=null}),E()),v[1].running.length>0?d?(d.p(v,A),2&A&&m(d,1)):(d=Tt(v),d.c(),m(d,1),d.m(e.parentNode,e)):d&&(q(),u(d,1,1,()=>{d=null}),E()),v[1].idle.length>0?g?(g.p(v,A),2&A&&m(g,1)):(g=Ft(v),g.c(),m(g,1),g.m(n.parentNode,n)):g&&(q(),u(g,1,1,()=>{g=null}),E()),v[1].failed.length>0?w?(w.p(v,A),2&A&&m(w,1)):(w=Nt(v),w.c(),m(w,1),w.m(a.parentNode,a)):w&&(q(),u(w,1,1,()=>{w=null}),E()),v[1].additional.length>0?h?(h.p(v,A),2&A&&m(h,1)):(h=Lt(v),h.c(),m(h,1),h.m(o.parentNode,o)):h&&(q(),u(h,1,1,()=>{h=null}),E())},i(v){l||(m(c),m(r),m(d),m(g),m(w),m(h),l=!0)},o(v){u(c),u(r),u(d),u(g),u(w),u(h),l=!1},d(v){v&&(p(t),p(s),p(e),p(n),p(a),p(o)),c&&c.d(v),r&&r.d(v),d&&d.d(v),g&&g.d(v),w&&w.d(v),h&&h.d(v)}}}function ls(i){let t,s,e;return s=new L({props:{size:3,color:"secondary",$$slots:{default:[ds]},$$scope:{ctx:i}}}),{c(){t=b("div"),y(s.$$.fragment),x(t,"class","empty-state svelte-aiqmvp")},m(n,a){$(n,t,a),_(s,t,null),e=!0},p(n,a){const o={};32768&a&&(o.$$scope={dirty:a,ctx:n}),s.$set(o)},i(n){e||(m(s.$$.fragment,n),e=!0)},o(n){u(s.$$.fragment,n),e=!1},d(n){n&&p(t),S(s)}}}function cs(i){let t,s,e,n,a,o;return e=new ae({}),a=new L({props:{size:3,color:"secondary",$$slots:{default:[gs]},$$scope:{ctx:i}}}),{c(){t=b("div"),s=b("div"),y(e.$$.fragment),n=B(),y(a.$$.fragment),x(s,"class","l-loading-container svelte-aiqmvp"),x(t,"class","empty-state svelte-aiqmvp")},m(l,c){$(l,t,c),O(t,s),_(e,s,null),O(s,n),_(a,s,null),o=!0},p(l,c){const r={};32768&c&&(r.$$scope={dirty:c,ctx:l}),a.$set(r)},i(l){o||(m(e.$$.fragment,l),m(a.$$.fragment,l),o=!0)},o(l){u(e.$$.fragment,l),u(a.$$.fragment,l),o=!1},d(l){l&&p(t),S(e),S(a)}}}function Et(i){let t,s,e,n,a=[],o=new Map;t=new st({props:{title:"Pinned"}});let l=H(i[1].pinned);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let d=qt(i,l,r),g=c(d);o.set(g,a[r]=Ot(g,d))}return{c(){y(t.$$.fragment),s=B(),e=b("div");for(let r=0;r<a.length;r+=1)a[r].c();x(e,"class","agent-grid svelte-aiqmvp")},m(r,d){_(t,r,d),$(r,s,d),$(r,e,d);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(e,null);n=!0},p(r,d){11&d&&(l=H(r[1].pinned),q(),a=X(a,d,c,1,r,l,o,e,Z,Ot,null,qt),E())},i(r){if(!n){m(t.$$.fragment,r);for(let d=0;d<l.length;d+=1)m(a[d]);n=!0}},o(r){u(t.$$.fragment,r);for(let d=0;d<a.length;d+=1)u(a[d]);n=!1},d(r){r&&(p(s),p(e)),S(t,r);for(let d=0;d<a.length;d+=1)a[d].d()}}}function Ot(i,t){var a;let s,e,n;return e=new et({props:{agent:t[7],selected:t[7].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),y(e.$$.fragment),this.first=s},m(o,l){$(o,s,l),_(e,o,l),n=!0},p(o,l){var r;t=o;const c={};2&l&&(c.agent=t[7]),3&l&&(c.selected=t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId)),e.$set(c)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),S(e,o)}}}function Mt(i){let t,s,e,n,a=[],o=new Map;t=new st({props:{title:"Ready to review"}});let l=H(i[1].readyToReview);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let d=Pt(i,l,r),g=c(d);o.set(g,a[r]=Ht(g,d))}return{c(){y(t.$$.fragment),s=B(),e=b("div");for(let r=0;r<a.length;r+=1)a[r].c();x(e,"class","agent-grid svelte-aiqmvp")},m(r,d){_(t,r,d),$(r,s,d),$(r,e,d);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(e,null);n=!0},p(r,d){11&d&&(l=H(r[1].readyToReview),q(),a=X(a,d,c,1,r,l,o,e,Z,Ht,null,Pt),E())},i(r){if(!n){m(t.$$.fragment,r);for(let d=0;d<l.length;d+=1)m(a[d]);n=!0}},o(r){u(t.$$.fragment,r);for(let d=0;d<a.length;d+=1)u(a[d]);n=!1},d(r){r&&(p(s),p(e)),S(t,r);for(let d=0;d<a.length;d+=1)a[d].d()}}}function Ht(i,t){var a;let s,e,n;return e=new et({props:{agent:t[7],selected:t[7].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),y(e.$$.fragment),this.first=s},m(o,l){$(o,s,l),_(e,o,l),n=!0},p(o,l){var r;t=o;const c={};2&l&&(c.agent=t[7]),3&l&&(c.selected=t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId)),e.$set(c)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),S(e,o)}}}function Tt(i){let t,s,e,n,a=[],o=new Map;t=new st({props:{title:"Running agents"}});let l=H(i[1].running);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let d=Bt(i,l,r),g=c(d);o.set(g,a[r]=Ct(g,d))}return{c(){y(t.$$.fragment),s=B(),e=b("div");for(let r=0;r<a.length;r+=1)a[r].c();x(e,"class","agent-grid svelte-aiqmvp")},m(r,d){_(t,r,d),$(r,s,d),$(r,e,d);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(e,null);n=!0},p(r,d){11&d&&(l=H(r[1].running),q(),a=X(a,d,c,1,r,l,o,e,Z,Ct,null,Bt),E())},i(r){if(!n){m(t.$$.fragment,r);for(let d=0;d<l.length;d+=1)m(a[d]);n=!0}},o(r){u(t.$$.fragment,r);for(let d=0;d<a.length;d+=1)u(a[d]);n=!1},d(r){r&&(p(s),p(e)),S(t,r);for(let d=0;d<a.length;d+=1)a[d].d()}}}function Ct(i,t){var a;let s,e,n;return e=new et({props:{agent:t[7],selected:t[7].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),y(e.$$.fragment),this.first=s},m(o,l){$(o,s,l),_(e,o,l),n=!0},p(o,l){var r;t=o;const c={};2&l&&(c.agent=t[7]),3&l&&(c.selected=t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId)),e.$set(c)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),S(e,o)}}}function Ft(i){let t,s,e,n,a=[],o=new Map;t=new st({props:{title:"Idle agents"}});let l=H(i[1].idle);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let d=bt(i,l,r),g=c(d);o.set(g,a[r]=Dt(g,d))}return{c(){y(t.$$.fragment),s=B(),e=b("div");for(let r=0;r<a.length;r+=1)a[r].c();x(e,"class","agent-grid svelte-aiqmvp")},m(r,d){_(t,r,d),$(r,s,d),$(r,e,d);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(e,null);n=!0},p(r,d){11&d&&(l=H(r[1].idle),q(),a=X(a,d,c,1,r,l,o,e,Z,Dt,null,bt),E())},i(r){if(!n){m(t.$$.fragment,r);for(let d=0;d<l.length;d+=1)m(a[d]);n=!0}},o(r){u(t.$$.fragment,r);for(let d=0;d<a.length;d+=1)u(a[d]);n=!1},d(r){r&&(p(s),p(e)),S(t,r);for(let d=0;d<a.length;d+=1)a[d].d()}}}function Dt(i,t){var a;let s,e,n;return e=new et({props:{agent:t[7],selected:t[7].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),y(e.$$.fragment),this.first=s},m(o,l){$(o,s,l),_(e,o,l),n=!0},p(o,l){var r;t=o;const c={};2&l&&(c.agent=t[7]),3&l&&(c.selected=t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId)),e.$set(c)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),S(e,o)}}}function Nt(i){let t,s,e,n,a=[],o=new Map;t=new st({props:{title:"Failed agents"}});let l=H(i[1].failed);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let d=It(i,l,r),g=c(d);o.set(g,a[r]=zt(g,d))}return{c(){y(t.$$.fragment),s=B(),e=b("div");for(let r=0;r<a.length;r+=1)a[r].c();x(e,"class","agent-grid svelte-aiqmvp")},m(r,d){_(t,r,d),$(r,s,d),$(r,e,d);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(e,null);n=!0},p(r,d){11&d&&(l=H(r[1].failed),q(),a=X(a,d,c,1,r,l,o,e,Z,zt,null,It),E())},i(r){if(!n){m(t.$$.fragment,r);for(let d=0;d<l.length;d+=1)m(a[d]);n=!0}},o(r){u(t.$$.fragment,r);for(let d=0;d<a.length;d+=1)u(a[d]);n=!1},d(r){r&&(p(s),p(e)),S(t,r);for(let d=0;d<a.length;d+=1)a[d].d()}}}function zt(i,t){var a;let s,e,n;return e=new et({props:{agent:t[7],selected:t[7].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),y(e.$$.fragment),this.first=s},m(o,l){$(o,s,l),_(e,o,l),n=!0},p(o,l){var r;t=o;const c={};2&l&&(c.agent=t[7]),3&l&&(c.selected=t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId)),e.$set(c)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),S(e,o)}}}function Lt(i){let t,s,e,n,a=[],o=new Map;t=new st({props:{title:"Other agents"}});let l=H(i[1].additional);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let d=kt(i,l,r),g=c(d);o.set(g,a[r]=Wt(g,d))}return{c(){y(t.$$.fragment),s=B(),e=b("div");for(let r=0;r<a.length;r+=1)a[r].c();x(e,"class","agent-grid svelte-aiqmvp")},m(r,d){_(t,r,d),$(r,s,d),$(r,e,d);for(let g=0;g<a.length;g+=1)a[g]&&a[g].m(e,null);n=!0},p(r,d){11&d&&(l=H(r[1].additional),q(),a=X(a,d,c,1,r,l,o,e,Z,Wt,null,kt),E())},i(r){if(!n){m(t.$$.fragment,r);for(let d=0;d<l.length;d+=1)m(a[d]);n=!0}},o(r){u(t.$$.fragment,r);for(let d=0;d<a.length;d+=1)u(a[d]);n=!1},d(r){r&&(p(s),p(e)),S(t,r);for(let d=0;d<a.length;d+=1)a[d].d()}}}function Wt(i,t){var a;let s,e,n;return e=new et({props:{agent:t[7],selected:t[7].remote_agent_id===((a=t[0].state)==null?void 0:a.selectedAgentId),onSelect:t[3]}}),{key:i,first:null,c(){s=D(),y(e.$$.fragment),this.first=s},m(o,l){$(o,s,l),_(e,o,l),n=!0},p(o,l){var r;t=o;const c={};2&l&&(c.agent=t[7]),3&l&&(c.selected=t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId)),e.$set(c)},i(o){n||(m(e.$$.fragment,o),n=!0)},o(o){u(e.$$.fragment,o),n=!1},d(o){o&&p(s),S(e,o)}}}function ds(i){let t;return{c(){t=T("No agents available")},m(s,e){$(s,t,e)},d(s){s&&p(t)}}}function gs(i){let t;return{c(){t=T("Loading the Augment panel...")},m(s,e){$(s,t,e)},d(s){s&&p(t)}}}function ms(i){let t,s,e,n;const a=[cs,ls,is],o=[];function l(c,r){var d,g;return(d=c[0].state)!=null&&d.agentOverviews?((g=c[0].state)==null?void 0:g.agentOverviews.length)===0?1:2:0}return s=l(i),e=o[s]=a[s](i),{c(){t=b("div"),e.c(),x(t,"class","agent-list svelte-aiqmvp")},m(c,r){$(c,t,r),o[s].m(t,null),n=!0},p(c,[r]){let d=s;s=l(c),s===d?o[s].p(c,r):(q(),u(o[d],1,1,()=>{o[d]=null}),E(),e=o[s],e?e.p(c,r):(e=o[s]=a[s](c),e.c()),m(e,1),e.m(t,null))},i(c){n||(m(e),n=!0)},o(c){u(e),n=!1},d(c){c&&p(t),o[s].d()}}}function us(i,t,s){let e,n,a,o;const l=lt(ut);Gt(i,l,r=>s(0,o=r));const c=lt(nt.key);return i.$$.update=()=>{var r,d,g;1&i.$$.dirty&&s(5,e=oe(((r=o.state)==null?void 0:r.agentOverviews)||[])),1&i.$$.dirty&&s(4,n=((d=o.state)==null?void 0:d.pinnedAgents)||{}),48&i.$$.dirty&&s(1,a=e.reduce((w,h)=>((n==null?void 0:n[h.remote_agent_id])===!0?w.pinned.push(h):h.status===z.agentIdle&&h.has_updates?w.readyToReview.push(h):h.status===z.agentRunning||h.status===z.agentStarting||h.workspace_status===gt.workspaceResuming?w.running.push(h):h.status===z.agentFailed?w.failed.push(h):h.status===z.agentIdle||h.workspace_status===gt.workspacePaused||h.workspace_status===gt.workspacePausing?w.idle.push(h):w.additional.push(h),w),{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]})),1&i.$$.dirty&&((g=o.state)!=null&&g.agentOverviews||c.focusAugmentPanel())},[o,a,l,function(r){l.update(d=>{if(d)return{...d,selectedAgentId:r}})},n,e]}class ps extends J{constructor(t){super(),K(this,t,us,ms,Q,{})}}function $s(i){let t,s,e,n,a,o,l,c,r,d;return n=new ye({}),l=new ps({}),{c(){t=b("div"),s=b("h1"),e=b("span"),y(n.$$.fragment),a=T(`
    Remote Agents`),o=B(),y(l.$$.fragment),x(e,"class","l-main__title-logo svelte-1941nw6"),x(s,"class","l-main__title svelte-1941nw6"),x(t,"class","l-main svelte-1941nw6")},m(g,w){$(g,t,w),O(t,s),O(s,e),_(n,e,null),O(s,a),O(t,o),_(l,t,null),c=!0,r||(d=jt(window,"message",i[0].onMessageFromExtension),r=!0)},p:tt,i(g){c||(m(n.$$.fragment,g),m(l.$$.fragment,g),c=!0)},o(g){u(n.$$.fragment,g),u(l.$$.fragment,g),c=!1},d(g){g&&p(t),S(n),S(l),r=!1,d()}}}function fs(i){const t=new ce(le),s=new _e(t,void 0,ue,pe);t.registerConsumer(s),vt(ut,s);const e=new nt(t);return vt(nt.key,e),re(()=>(s.fetchStateFromExtension().then(()=>{s.update(n=>{if(!n)return;const a=[...n.activeWebviews,"home"];return n.pinnedAgents?{...n,activeWebviews:a}:{...n,activeWebviews:a,pinnedAgents:{}}})}),()=>{t.dispose(),e.dispose()})),[t]}new class extends J{constructor(i){super(),K(this,i,fs,$s,Q,{})}}({target:document.getElementById("app")});
