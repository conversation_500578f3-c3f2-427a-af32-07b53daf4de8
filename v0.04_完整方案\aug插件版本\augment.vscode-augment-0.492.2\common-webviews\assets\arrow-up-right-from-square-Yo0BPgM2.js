var J=Object.defineProperty;var Q=(t,e,a)=>e in t?J(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a;var o=(t,e,a)=>Q(t,typeof e!="symbol"?e+"":e,a);import{c as X,f as Y,T as C,d as Z,e as ee,p as te,W as ae,g as f,R as se,h as k,i as O,r as P,s as D,j as F,S as ne,k as re}from"./types-DK4HA_lx.js";import{W as c}from"./BaseButton-DBHsDlhs.js";import{a as ie,C as V,P as q}from"./TextTooltipAugment-DAVq7Vla.js";import{n as oe}from"./file-paths-BcSg4gks.js";import{S as ce,i as de,s as le,a as L,b as ue,H as ye,w as ge,x as he,y as pe,h as H,d as N,z as me,g as fe,n as $,j}from"./SpinnerAugment-BEPEN2tu.js";var I;function G(t){const e=I[t];return typeof e!="string"?t.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,a=>"_"+a.toLowerCase())}(function(t){t[t.Canceled=1]="Canceled",t[t.Unknown=2]="Unknown",t[t.InvalidArgument=3]="InvalidArgument",t[t.DeadlineExceeded=4]="DeadlineExceeded",t[t.NotFound=5]="NotFound",t[t.AlreadyExists=6]="AlreadyExists",t[t.PermissionDenied=7]="PermissionDenied",t[t.ResourceExhausted=8]="ResourceExhausted",t[t.FailedPrecondition=9]="FailedPrecondition",t[t.Aborted=10]="Aborted",t[t.OutOfRange=11]="OutOfRange",t[t.Unimplemented=12]="Unimplemented",t[t.Internal=13]="Internal",t[t.Unavailable=14]="Unavailable",t[t.DataLoss=15]="DataLoss",t[t.Unauthenticated=16]="Unauthenticated"})(I||(I={}));class R extends Error{constructor(e,a=I.Unknown,s,n,i){super(function(r,l){return r.length?`[${G(l)}] ${r}`:`[${G(l)}]`}(e,a)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=a,this.metadata=new Headers(s??{}),this.details=n??[],this.cause=i}static from(e,a=I.Unknown){return e instanceof R?e:e instanceof Error?e.name=="AbortError"?new R(e.message,I.Canceled):new R(e.message,a,void 0,void 0,e):new R(String(e),a,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===R.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const a=e.kind==="message"?{getMessage:n=>n===e.typeName?e:void 0}:e,s=[];for(const n of this.details){if("desc"in n){a.getMessage(n.desc.typeName)&&s.push(X(n.desc,n.value));continue}const i=a.getMessage(n.type);if(i)try{s.push(Y(i,n.value))}catch{}}return s}}var _e=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,a=t[Symbol.asyncIterator];return a?a.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(n){e[n]=t[n]&&function(i){return new Promise(function(r,l){(function(u,p,_,d){Promise.resolve(d).then(function(y){u({value:y,done:_})},p)})(r,l,(i=t[n](i)).done,i.value)})}}},A=function(t){return this instanceof A?(this.v=t,this):new A(t)},Se=function(t,e,a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,n=a.apply(t,e||[]),i=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",function(d){return function(y){return Promise.resolve(y).then(d,p)}}),s[Symbol.asyncIterator]=function(){return this},s;function r(d,y){n[d]&&(s[d]=function(h){return new Promise(function(m,M){i.push([d,h,m,M])>1||l(d,h)})},y&&(s[d]=y(s[d])))}function l(d,y){try{(h=n[d](y)).value instanceof A?Promise.resolve(h.value.v).then(u,p):_(i[0][2],h)}catch(m){_(i[0][3],m)}var h}function u(d){l("next",d)}function p(d){l("throw",d)}function _(d,y){d(y),i.shift(),i.length&&l(i[0][0],i[0][1])}},ve=function(t){var e,a;return e={},s("next"),s("throw",function(n){throw n}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(n,i){e[n]=t[n]?function(r){return(a=!a)?{value:A(t[n](r)),done:!1}:i?i(r):r}:i}},z=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,a=t[Symbol.asyncIterator];return a?a.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(n){e[n]=t[n]&&function(i){return new Promise(function(r,l){(function(u,p,_,d){Promise.resolve(d).then(function(y){u({value:y,done:_})},p)})(r,l,(i=t[n](i)).done,i.value)})}}},T=function(t){return this instanceof T?(this.v=t,this):new T(t)},we=function(t){var e,a;return e={},s("next"),s("throw",function(n){throw n}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(n,i){e[n]=t[n]?function(r){return(a=!a)?{value:T(t[n](r)),done:!1}:i?i(r):r}:i}},Me=function(t,e,a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,n=a.apply(t,e||[]),i=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",function(d){return function(y){return Promise.resolve(y).then(d,p)}}),s[Symbol.asyncIterator]=function(){return this},s;function r(d,y){n[d]&&(s[d]=function(h){return new Promise(function(m,M){i.push([d,h,m,M])>1||l(d,h)})},y&&(s[d]=y(s[d])))}function l(d,y){try{(h=n[d](y)).value instanceof T?Promise.resolve(h.value.v).then(u,p):_(i[0][2],h)}catch(m){_(i[0][3],m)}var h}function u(d){l("next",d)}function p(d){l("throw",d)}function _(d,y){d(y),i.shift(),i.length&&l(i[0][0],i[0][1])}};function Re(t,e){return function(a,s){const n={};for(const i of a.methods){const r=s(i);r!=null&&(n[i.localName]=r)}return n}(t,a=>{switch(a.methodKind){case"unary":return function(s,n){return async function(i,r){var l,u;const p=await s.unary(n,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,i,r==null?void 0:r.contextValues);return(l=r==null?void 0:r.onHeader)===null||l===void 0||l.call(r,p.header),(u=r==null?void 0:r.onTrailer)===null||u===void 0||u.call(r,p.trailer),p.message}}(e,a);case"server_streaming":return function(s,n){return function(i,r){return W(s.stream(n,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,function(l){return Se(this,arguments,function*(){yield A(yield*ve(_e(l)))})}([i]),r==null?void 0:r.contextValues),r)}}(e,a);case"client_streaming":return function(s,n){return async function(i,r){var l,u,p,_,d,y;const h=await s.stream(n,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,i,r==null?void 0:r.contextValues);let m;(d=r==null?void 0:r.onHeader)===null||d===void 0||d.call(r,h.header);let M=0;try{for(var g,b=!0,E=z(h.message);!(l=(g=await E.next()).done);b=!0)_=g.value,b=!1,m=_,M++}catch(K){u={error:K}}finally{try{b||l||!(p=E.return)||await p.call(E)}finally{if(u)throw u.error}}if(!m)throw new R("protocol error: missing response message",I.Unimplemented);if(M>1)throw new R("protocol error: received extra messages for client streaming method",I.Unimplemented);return(y=r==null?void 0:r.onTrailer)===null||y===void 0||y.call(r,h.trailer),m}}(e,a);case"bidi_streaming":return function(s,n){return function(i,r){return W(s.stream(n,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,i,r==null?void 0:r.contextValues),r)}}(e,a);default:return null}})}function W(t,e){const a=function(){return Me(this,arguments,function*(){var s,n;const i=yield T(t);(s=e==null?void 0:e.onHeader)===null||s===void 0||s.call(e,i.header),yield T(yield*we(z(i.message))),(n=e==null?void 0:e.onTrailer)===null||n===void 0||n.call(e,i.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>a.next()})}}const Ke="augment-welcome";var S=(t=>(t.draft="draft",t.sent="sent",t.failed="failed",t.success="success",t.cancelled="cancelled",t))(S||{}),Ie=(t=>(t.running="running",t.awaitingUserAction="awaiting-user-action",t.notRunning="not-running",t))(Ie||{}),w=(t=>(t.seen="seen",t.unseen="unseen",t))(w||{}),Te=(t=>(t.signInWelcome="sign-in-welcome",t.generateCommitMessage="generate-commit-message",t.summaryResponse="summary-response",t.summaryTitle="summary-title",t.educateFeatures="educate-features",t.autofixMessage="autofix-message",t.autofixSteeringMessage="autofix-steering-message",t.autofixStage="autofix-stage",t.agentOnboarding="agent-onboarding",t.agenticTurnDelimiter="agentic-turn-delimiter",t.agenticRevertDelimiter="agentic-revert-delimiter",t.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",t.exchange="exchange",t.historySummary="history-summary",t))(Te||{});function be(t){return!!t&&(t.chatItemType===void 0||t.chatItemType==="agent-onboarding")}function Je(t){return be(t)&&t.status==="success"}function Qe(t){return t.chatItemType==="autofix-message"}function Xe(t){return t.chatItemType==="autofix-steering-message"}function Ye(t){return t.chatItemType==="autofix-stage"}function Ze(t){return t.chatItemType==="sign-in-welcome"}function et(t){return t.chatItemType==="generate-commit-message"}function tt(t){return t.chatItemType==="summary-response"}function at(t){return t.chatItemType==="educate-features"}function st(t){return t.chatItemType==="agent-onboarding"}function nt(t){return t.chatItemType==="agentic-turn-delimiter"}function rt(t){return t.chatItemType==="agentic-checkpoint-delimiter"}function it(t){return t.chatItemType==="history-summary"}function ot(t){return t.revertTarget!==void 0}function ct(t){var e;return((e=t.structured_output_nodes)==null?void 0:e.some(a=>a.type===V.TOOL_USE))??!1}function dt(t){var e;return((e=t.structured_request_nodes)==null?void 0:e.some(a=>a.type===ie.TOOL_RESULT))??!1}function lt(t){return!(!t||typeof t!="object")&&(!("request_id"in t)||typeof t.request_id=="string")&&(!("seen_state"in t)||t.seen_state==="seen"||t.seen_state==="unseen")}async function*Ce(t,e=1e3){for(;t>0;)yield t,await new Promise(a=>setTimeout(a,Math.min(e,t))),t-=e}class xe{constructor(e,a,s,n=5,i=4e3,r){o(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=a,this.startStreamFn=s,this.maxRetries=n,this.baseDelay=i,this.flags=r}cancel(){this._isCancelled=!0}async*getStream(){let e=0,a=!1;try{for(;!this._isCancelled;){const s=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let n,i=!1,r="";for await(const u of s){if(u.status===S.failed){if(u.isRetriable!==!0||a)return yield u;i=!0,r=u.display_error_message||"Service is currently unavailable",n=u.request_id;break}a=!0,yield u}if(!i)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return void(yield{request_id:n??this.requestId,seen_state:w.unseen,status:S.failed,display_error_message:r,isRetriable:!1});const l=this.baseDelay*2**(e-1);for await(const u of Ce(l))yield{request_id:this.requestId,status:S.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(u/1e3)} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0};yield{request_id:this.requestId,status:S.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(s){yield{request_id:this.requestId,seen_state:w.unseen,status:S.failed,display_error_message:s instanceof Error?s.message:String(s)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:w.unseen,status:S.cancelled}}}class Ae{constructor(e){o(this,"getHydratedTask",async e=>{const a={type:C.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.task});o(this,"createTask",async(e,a,s)=>{const n={type:C.createTaskRequest,data:{name:e,description:a,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.uuid});o(this,"updateTask",async(e,a,s)=>{const n={type:C.updateTaskRequest,data:{uuid:e,updates:a,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(n,3e4)});o(this,"setCurrentRootTaskUuid",e=>{const a={type:C.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(a)});o(this,"updateHydratedTask",async(e,a)=>{const s={type:C.updateHydratedTaskRequest,data:{task:e,updatedBy:a}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=e}}function v(t,e){return e in t&&t[e]!==void 0}function qe(t){return v(t,"file")}function Ee(t){return v(t,"recentFile")}function ke(t){return v(t,"folder")}function Pe(t){return v(t,"sourceFolder")}function ut(t){return v(t,"sourceFolderGroup")}function yt(t){return v(t,"selection")}function Fe(t){return v(t,"externalSource")}function gt(t){return v(t,"allDefaultContext")}function ht(t){return v(t,"clearContext")}function pt(t){return v(t,"userGuidelines")}function mt(t){return v(t,"agentMemories")}function Ue(t){return v(t,"personality")}function Le(t){return v(t,"rule")}const ft={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},_t={clearContext:!0,label:"Clear Context",id:"clearContext"},St={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},vt={agentMemories:{},label:"Agent Memories",id:"agentMemories"},B=[{personality:{type:q.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:q.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:q.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:q.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function wt(t){return v(t,"group")}function Mt(t){const e=new Map;return t.forEach(a=>{qe(a)?e.set("file",[...e.get("file")??[],a]):Ee(a)?e.set("recentFile",[...e.get("recentFile")??[],a]):ke(a)?e.set("folder",[...e.get("folder")??[],a]):Fe(a)?e.set("externalSource",[...e.get("externalSource")??[],a]):Pe(a)?e.set("sourceFolder",[...e.get("sourceFolder")??[],a]):Ue(a)?e.set("personality",[...e.get("personality")??[],a]):Le(a)&&e.set("rule",[...e.get("rule")??[],a])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(a=>a.group.items.length>0)}function Oe(t){const e=te({rootPath:t.repoRoot,relPath:t.pathName}),a={label:oe(t.pathName).split("/").filter(s=>s.trim()!=="").pop()||"",name:e,id:e};if(t.fullRange){const s=`:L${t.fullRange.startLineNumber}-${t.fullRange.endLineNumber}`;a.label+=s,a.name+=s,a.id+=s}else if(t.range){const s=`:L${t.range.start}-${t.range.stop}`;a.label+=s,a.name+=s,a.id+=s}return a}function De(t){const e=t.path.split("/"),a=e[e.length-1],s=a.endsWith(".md")?a.slice(0,-3):a,n=`${Z}/${ee}/${t.path}`;return{label:s,name:n,id:n}}class Rt{constructor(e,a,s){o(this,"_taskClient");o(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:c.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const a=await async function(s){return(await Re(re,new ne({sendMessage:i=>{s.postMessage(i)},onReceiveMessage:i=>{const r=l=>{i(l.data)};return window.addEventListener("message",r),()=>{window.removeEventListener("message",r)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",a)}catch(a){console.error("Hello world error:",a)}return e.data});o(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:c.reportWebviewClientMetric,data:{webviewName:ae.chat,client_metric:e,value:1}})});o(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:f.reportAgentSessionEvent,data:e})});o(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:f.reportAgentRequestEvent,data:e})});o(this,"getSuggestions",async(e,a=!1)=>{const s={rootPath:"",relPath:e},n=this.findFiles(s,6),i=this.findRecentlyOpenedFiles(s,6),r=this.findFolders(s,3),l=this.findExternalSources(e,a),u=this._flags.enableRules?this.findRules(e,6):Promise.resolve([]),[p,_,d,y,h]=await Promise.all([x(n,[]),x(i,[]),x(r,[]),x(l,[]),x(u,[])]),m=(g,b)=>({...Oe(g),[b]:g}),M=[...p.map(g=>m(g,"file")),...d.map(g=>m(g,"folder")),..._.map(g=>m(g,"recentFile")),...y.map(g=>({label:g.name,name:g.name,id:g.id,externalSource:g})),...h.map(g=>({...De(g),rule:g}))];if(this._flags.enablePersonalities){const g=this.getPersonalities(e);g.length>0&&M.push(...g)}return M});o(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return B;const a=e.toLowerCase();return B.filter(s=>{const n=s.personality.description.toLowerCase(),i=s.label.toLowerCase();return n.includes(a)||i.includes(a)})});o(this,"sendAction",e=>{this._host.postMessage({type:c.mainPanelPerformAction,data:e})});o(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:c.showAugmentPanel})});o(this,"showNotification",e=>{this._host.postMessage({type:c.showNotification,data:e})});o(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:c.openConfirmationModal,data:e},1e9)).data.ok);o(this,"clearMetadataFor",e=>{this._host.postMessage({type:c.chatClearMetadata,data:e})});o(this,"resolvePath",async(e,a=void 0)=>{const s=await this._asyncMsgSender.send({type:c.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:a}},5e3);if(s.data)return s.data});o(this,"resolveSymbols",async(e,a)=>(await this._asyncMsgSender.send({type:c.findSymbolRequest,data:{query:e,searchScope:a}},3e4)).data);o(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:c.getDiagnosticsRequest},1e3)).data);o(this,"findFiles",async(e,a=12)=>(await this._asyncMsgSender.send({type:c.findFileRequest,data:{...e,maxResults:a}},5e3)).data);o(this,"findFolders",async(e,a=12)=>(await this._asyncMsgSender.send({type:c.findFolderRequest,data:{...e,maxResults:a}},5e3)).data);o(this,"findRecentlyOpenedFiles",async(e,a=12)=>(await this._asyncMsgSender.send({type:c.findRecentlyOpenedFilesRequest,data:{...e,maxResults:a}},5e3)).data);o(this,"findExternalSources",async(e,a=!1)=>this._flags.enableExternalSourcesInChat?a?[]:(await this._asyncMsgSender.send({type:c.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);o(this,"findRules",async(e,a=12)=>(await this._asyncMsgSender.sendToSidecar({type:se.getRulesListRequest,data:{query:e,maxResults:a}})).data.rules);o(this,"openFile",e=>{this._host.postMessage({type:c.openFile,data:e})});o(this,"saveFile",e=>this._host.postMessage({type:c.saveFile,data:e}));o(this,"loadFile",e=>this._host.postMessage({type:c.loadFile,data:e}));o(this,"openMemoriesFile",()=>{this._host.postMessage({type:c.openMemoriesFile})});o(this,"createFile",(e,a)=>{this._host.postMessage({type:c.chatCreateFile,data:{code:e,relPath:a}})});o(this,"openScratchFile",async(e,a="shellscript")=>{await this._asyncMsgSender.send({type:c.openScratchFileRequest,data:{content:e,language:a}},1e4)});o(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:c.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});o(this,"smartPaste",e=>{this._host.postMessage({type:c.chatSmartPaste,data:e})});o(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));o(this,"updateHydratedTask",async(e,a)=>this._taskClient.updateHydratedTask(e,a));o(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});o(this,"createTask",async(e,a,s)=>this._taskClient.createTask(e,a,s));o(this,"updateTask",async(e,a,s)=>this._taskClient.updateTask(e,a,s));o(this,"saveChat",async(e,a,s)=>this._asyncMsgSender.send({type:c.saveChat,data:{conversationId:e,chatHistory:a,title:s}},5e3));o(this,"launchAutofixPanel",async(e,a,s)=>this._asyncMsgSender.send({type:c.chatLaunchAutofixPanel,data:{conversationId:e,iterationId:a,stage:s}}));o(this,"updateUserGuidelines",e=>{this._host.postMessage({type:c.updateUserGuidelines,data:e})});o(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:c.updateWorkspaceGuidelines,data:e})});o(this,"updateRuleFile",(e,a)=>{this._host.postMessage({type:c.updateRuleFile,data:{rulePath:e,content:a}})});o(this,"openSettingsPage",e=>{this._host.postMessage({type:c.openSettingsPage,data:e})});o(this,"_activeRetryStreams",new Map);o(this,"cancelChatStream",async e=>{var a;(a=this._activeRetryStreams.get(e))==null||a.cancel(),await this._asyncMsgSender.send({type:c.chatUserCancel,data:{requestId:e}},1e4)});o(this,"sendUserRating",async(e,a,s,n="")=>{const i={requestId:e,rating:s,note:n,mode:a},r={type:c.chatRating,data:i};return(await this._asyncMsgSender.send(r,3e4)).data});o(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:c.usedChat})});o(this,"createProject",e=>{this._host.postMessage({type:c.mainPanelCreateProject,data:{name:e}})});o(this,"openProjectFolder",()=>{this._host.postMessage({type:c.mainPanelPerformAction,data:"open-folder"})});o(this,"closeProjectFolder",()=>{this._host.postMessage({type:c.mainPanelPerformAction,data:"close-folder"})});o(this,"cloneRepository",()=>{this._host.postMessage({type:c.mainPanelPerformAction,data:"clone-repository"})});o(this,"grantSyncPermission",()=>{this._host.postMessage({type:c.mainPanelPerformAction,data:"grant-sync-permission"})});o(this,"callTool",async(e,a,s,n,i,r)=>{const l={type:c.callTool,data:{chatRequestId:e,toolUseId:a,name:s,input:n,chatHistory:i,conversationId:r}};return(await this._asyncMsgSender.send(l,0)).data});o(this,"cancelToolRun",async(e,a)=>{const s={type:c.cancelToolRun,data:{requestId:e,toolUseId:a}};await this._asyncMsgSender.send(s,0)});o(this,"checkSafe",async e=>{const a={type:k.checkToolCallSafeRequest,data:e};return(await this._asyncMsgSender.sendToSidecar(a,0)).data});o(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:k.closeAllToolProcesses},0)});o(this,"getToolIdentifier",async e=>{const a={type:k.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(a,0)).data});o(this,"executeCommand",async(e,a,s)=>{try{const n=await this._asyncMsgSender.send({type:c.chatAutofixExecuteCommandRequest,data:{iterationId:e,command:a,args:s}},6e5);return{output:n.data.output,returnCode:n.data.returnCode}}catch(n){throw console.error("[ExtensionClient] Execute command failed:",n),n}});o(this,"sendAutofixStateUpdate",async e=>{await this._asyncMsgSender.send({type:c.chatAutofixStateUpdate,data:e})});o(this,"autofixPlan",async(e,a)=>(await this._asyncMsgSender.send({type:c.chatAutofixPlanRequest,data:{command:e,steeringHistory:a}},6e4)).data.plan);o(this,"setChatMode",e=>{this._asyncMsgSender.send({type:c.chatModeChanged,data:{mode:e}})});o(this,"setLastUsedChatMode",e=>{this._asyncMsgSender.send({type:c.chatSetLastUsedChatMode,data:{mode:e}}).catch(a=>{console.error("Failed to set last used chat mode:",a)})});o(this,"getAgentEditList",async(e,a)=>{const s={type:f.getEditListRequest,data:{fromTimestamp:e,toTimestamp:a}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"hasChangesSince",async e=>{const a={type:f.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.edits.filter(s=>{var n,i;return((n=s.changesSummary)==null?void 0:n.totalAddedLines)||((i=s.changesSummary)==null?void 0:i.totalRemovedLines)}).length>0});o(this,"getToolCallCheckpoint",async e=>{const a={type:c.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(a,3e4)).data.checkpointNumber});o(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:f.setCurrentConversation,data:{conversationId:e}})});o(this,"migrateConversationId",async(e,a)=>{await this._asyncMsgSender.sendToSidecar({type:f.migrateConversationId,data:{oldConversationId:e,newConversationId:a}},3e4)});o(this,"showAgentReview",(e,a,s,n=!0,i)=>{this._asyncMsgSender.sendToSidecar({type:f.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:a,toTimestamp:s,retainFocus:n,useNativeDiffIfAvailable:i}})});o(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:f.chatAgentEditAcceptAll}),!0));o(this,"revertToTimestamp",async(e,a)=>(await this._asyncMsgSender.sendToSidecar({type:f.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:a}}),!0));o(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:c.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);o(this,"getAgentEditChangesByRequestId",async e=>{const a={type:f.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data});o(this,"getAgentEditContentsByRequestId",async e=>{const a={type:f.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data});o(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:c.triggerInitialOrientation})});o(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:c.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});o(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:c.toggleCollapseUnchangedRegions})});o(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:c.checkAgentAutoModeApproval},5e3)).data);o(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:c.setAgentAutoModeApproved,data:e},5e3)});o(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:f.checkHasEverUsedAgent},5e3)).data);o(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:f.setHasEverUsedAgent,data:e},5e3)});o(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:f.checkHasEverUsedRemoteAgent},5e3)).data);o(this,"setHasEverUsedRemoteAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:f.setHasEverUsedRemoteAgent,data:e},5e3)});o(this,"getChatRequestIdeState",async()=>{const e={type:c.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});o(this,"reportError",e=>{this._host.postMessage({type:c.reportError,data:e})});this._host=e,this._asyncMsgSender=a,this._flags=s,this._taskClient=new Ae(a)}async*generateCommitMessage(){const e={type:c.generateCommitMessage},a=this._asyncMsgSender.stream(e,3e4,6e4);yield*U(a)}async*sendInstructionMessage(e,a){const s={instruction:e.request_message??"",selectedCodeDetails:a,requestId:e.request_id},n={type:c.chatInstructionMessage,data:s},i=this._asyncMsgSender.stream(n,3e4,6e4);yield*async function*(r){let l;try{for await(const u of r)l=u.data.requestId,yield{request_id:l,response_text:u.data.text,seen_state:w.unseen,status:S.sent};yield{request_id:l,seen_state:w.unseen,status:S.success}}catch{yield{request_id:l,seen_state:w.unseen,status:S.failed}}}(i)}async openGuidelines(e){this._host.postMessage({type:c.openGuidelines,data:e})}async*getExistingChatStream(e,a){if(!e.request_id)return;const s=a==null?void 0:a.flags.enablePreferenceCollection,n=s?1e9:6e4,i=s?1e9:3e5,r={type:c.chatGetStreamRequest,data:{requestId:e.request_id}},l=this._asyncMsgSender.stream(r,n,i);yield*U(l,this.reportError)}async*startChatStream(e,a){const s=a==null?void 0:a.flags.enablePreferenceCollection,n=s?1e9:1e5,i=s?1e9:3e5,r={type:c.chatUserMessage,data:e},l=this._asyncMsgSender.stream(r,n,i);yield*U(l,this.reportError)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:c.checkToolExists,toolName:e},0)).exists}async saveImage(e,a){const s=O(await P(e)),n=a??`${await D(await F(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:c.chatSaveImageRequest,data:{filename:n,data:s}},1e4)).data}async saveAttachment(e,a){const s=O(await P(e)),n=a??`${await D(await F(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:c.chatSaveAttachmentRequest,data:{filename:n,data:s}},1e4)).data}async loadImage(e){const a=await this._asyncMsgSender.send({type:c.chatLoadImageRequest,data:e},1e4),s=a.data?await F(a.data):void 0;if(!s)return;let n="application/octet-stream";const i=e.split(".").at(-1);i==="png"?n="image/png":i!=="jpg"&&i!=="jpeg"||(n="image/jpeg");const r=new File([s],e,{type:n});return await P(r)}async deleteImage(e){await this._asyncMsgSender.send({type:c.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,a,s){const n=new xe(e,a,(i,r)=>this.startChatStream(i,r),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(e,n);try{yield*n.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:c.getSubscriptionInfo},5e3)}}async function*U(t,e=()=>{}){let a;try{for await(const s of t){if(a=s.data.requestId,s.data.error)return yield{request_id:a,seen_state:w.unseen,status:S.failed,display_error_message:s.data.error.displayErrorMessage,isRetriable:s.data.error.isRetriable};const n={request_id:a,response_text:s.data.text,workspace_file_chunks:s.data.workspaceFileChunks,structured_output_nodes:He(s.data.nodes),seen_state:w.unseen,status:S.sent};s.data.stop_reason!=null&&(n.stop_reason=s.data.stop_reason),yield n}yield{request_id:a,seen_state:w.unseen,status:S.success}}catch(s){e({originalRequestId:a||"",sanitizedMessage:s instanceof Error?s.message:String(s),stackTrace:s instanceof Error&&s.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:a,seen_state:w.unseen,status:S.failed}}}async function x(t,e){try{return await t}catch(a){return console.warn(`Error while resolving promise: ${a}`),e}}function He(t){if(!t)return t;let e=!1;return t.filter(a=>a.type!==V.TOOL_USE||!e&&(e=!0,!0))}function Ne(t){let e,a,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},t[0]],n={};for(let i=0;i<s.length;i+=1)n=L(n,s[i]);return{c(){e=ue("svg"),a=new ye(!0),this.h()},l(i){e=ge(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=he(e);a=pe(r,!0),r.forEach(H),this.h()},h(){a.a=null,N(e,n)},m(i,r){me(i,e,r),a.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 24c0 13.3 10.7 24 24 24h102.1L207 271c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l223-223L464 184c0 13.3 10.7 24 24 24s24-10.7 24-24V24c0-13.3-10.7-24-24-24H328c-13.3 0-24 10.7-24 24M72 32C32.2 32 0 64.2 0 104v336c0 39.8 32.2 72 72 72h336c39.8 0 72-32.2 72-72V312c0-13.3-10.7-24-24-24s-24 10.7-24 24v128c0 13.3-10.7 24-24 24H72c-13.3 0-24-10.7-24-24V104c0-13.3 10.7-24 24-24h128c13.3 0 24-10.7 24-24s-10.7-24-24-24z"/>',e)},p(i,[r]){N(e,n=fe(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&i[0]]))},i:$,o:$,d(i){i&&H(e)}}}function $e(t,e,a){return t.$$set=s=>{a(0,e=L(L({},e),j(s)))},[e=j(e)]}class It extends ce{constructor(e){super(),de(this,e,$e,Ne,le,{})}}export{It as A,pt as B,Te as C,vt as D,S as E,Le as F,Oe as G,De as H,nt as I,ut as J,mt as K,Mt as L,wt as M,gt as N,_t as O,St as P,ht as Q,w as S,ft as U,Ze as a,at as b,tt as c,Qe as d,Xe as e,Ye as f,be as g,et as h,lt as i,st as j,rt as k,dt as l,Rt as m,Ie as n,ct as o,ot as p,Je as q,it as r,Ue as s,Ke as t,qe as u,Ee as v,yt as w,ke as x,Pe as y,Fe as z};
