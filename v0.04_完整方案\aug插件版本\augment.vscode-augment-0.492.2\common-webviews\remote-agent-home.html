<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agents</title>
    <script nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <script type="module" crossorigin src="./assets/remote-agent-home-B6eRagNk.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BEPEN2tu.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BG5cEFim.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/index-DhtTPDph.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-DWM9BlRb.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-DBHsDlhs.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DT9AU8SC.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/Content-Bm7C6iJ1.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-DAVq7Vla.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/types-DDm27S8B.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/utils-Rh_q5w_c.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/index-DqflzTEb.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-By29C4Fu.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-RCmwRtRa.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-DzngRWi1.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-CS_V-nAg.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-Ovva0uhO.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-DTgokSKV.js" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-Be7obQsv.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/index-DL-lqibn.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-B2NZuaj3.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-CXnRMJBa.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-D-yOSWp9.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/Content-LuLOeTld.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BuBr3xQZ.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-CA6XnfI-.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-Dvw-pMXw.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-home-CuWF5Lfd.css" nonce="nonce-dTTNeXUT7CC0UEmLP0Mbww==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
