var h=Object.defineProperty;var y=(s,e,t)=>e in s?h(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var o=(s,e,t)=>y(s,typeof e!="symbol"?e+"":e,t);import{W as i}from"./BaseButton-DBHsDlhs.js";import{A as c}from"./SpinnerAugment-BEPEN2tu.js";class u{constructor(e){o(this,"_applyingFilePaths",c([]));o(this,"_appliedFilePaths",c([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,r=3e4){try{return(await this._asyncMsgSender.send({type:i.diffExplanationRequest,data:{changedFiles:e,apikey:t}},r)).data.explanation}catch(a){return console.error("Failed to get diff explanation:",a),[]}}async groupChanges(e,t=!1,r){try{return(await this._asyncMsgSender.send({type:i.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:r}})).data.groupedChanges}catch(a){return console.error("Failed to group changes:",a),[]}}async getDescriptions(e,t){try{const r=await this._asyncMsgSender.send({type:i.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}},1e5);return{explanation:r.data.explanation,error:r.data.error}}catch(r){return console.error("Failed to get descriptions:",r),{explanation:[],error:`Failed to get descriptions: ${r instanceof Error?r.message:String(r)}`}}}async applyChanges(e,t,r){this._applyingFilePaths.update(a=>[...a.filter(n=>n!==e),e]);try{const a=await this._asyncMsgSender.send({type:i.applyChangesRequest,data:{path:e,originalCode:t,newCode:r}},3e4),{success:n,hasConflicts:l,error:p}=a.data;return n?this._appliedFilePaths.update(d=>[...d.filter(g=>g!==e),e]):p&&console.error("Failed to apply changes:",p),{success:n,hasConflicts:l,error:p}}catch(a){return console.error("applyChanges error",a),{success:!1,error:`Error: ${a instanceof Error?a.message:String(a)}`}}finally{this._applyingFilePaths.update(a=>a.filter(n=>n!==e))}}async openFile(e){try{const t=await this._asyncMsgSender.send({type:i.openFileRequest,data:{path:e}},1e4);return t.data.success||console.error("Failed to open file:",t.data.error),t.data.success}catch(t){console.error("openFile error",t)}return!1}async reportApplyChangesEvent(){await this._asyncMsgSender.send({type:i.reportAgentChangesApplied})}}o(u,"key","remoteAgentsDiffOpsModel");export{u as R};
